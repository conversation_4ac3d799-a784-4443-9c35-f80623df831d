# 教育培训系统详细设计方案

## 一、系统概述

### 1.1 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    前端应用层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  管理端界面  │  │  教官端界面  │  │  学员端界面  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
                         HTTP/HTTPS
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Spring Boot 应用                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  控制器层    │  │   服务层     │  │  数据访问层  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
                              │
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ PostgreSQL  │  │    Redis    │  │    MinIO    │        │
│  │   主数据库   │  │   会话缓存   │  │  文件存储    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 核心业务模块
1. **用户认证模块** - 用户登录、权限管理
2. **教培资源管理模块** - 课程、教官、教案、考题管理
3. **教培计划编制模块** - 培训计划创建、审核、发布
4. **在线学习模块** - 学习任务、进度跟踪
5. **在线考试模块** - 试卷生成、考试执行、成绩管理
6. **文件管理模块** - 文件上传、存储、下载
7. **系统管理模块** - 系统配置、日志管理

## 二、数据库详细设计

### 2.1 数据库Schema设计
```sql
-- 创建Schema
CREATE SCHEMA IF NOT EXISTS training_system;
SET search_path TO training_system;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA training_system GRANT ALL ON TABLES TO postgres;
ALTER DEFAULT PRIVILEGES IN SCHEMA training_system GRANT ALL ON SEQUENCES TO postgres;
```

### 2.2 用户认证相关表

#### 2.2.1 用户表 (sys_user)
```sql
CREATE TABLE sys_user (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(100) NOT NULL,
    real_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    avatar_url VARCHAR(500),
    department_id BIGINT,
    position VARCHAR(50),
    employee_no VARCHAR(50),
    status SMALLINT DEFAULT 1, -- 1:正常 0:禁用 2:锁定
    last_login_time TIMESTAMP,
    last_login_ip VARCHAR(50),
    password_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT
);

-- 添加索引
CREATE INDEX idx_user_username ON sys_user(username);
CREATE INDEX idx_user_department ON sys_user(department_id);
CREATE INDEX idx_user_status ON sys_user(status);
CREATE INDEX idx_user_employee_no ON sys_user(employee_no);

-- 添加注释
COMMENT ON TABLE sys_user IS '用户表';
COMMENT ON COLUMN sys_user.username IS '用户名';
COMMENT ON COLUMN sys_user.password IS '密码(BCrypt加密)';
COMMENT ON COLUMN sys_user.real_name IS '真实姓名';
COMMENT ON COLUMN sys_user.phone IS '手机号';
COMMENT ON COLUMN sys_user.email IS '邮箱';
COMMENT ON COLUMN sys_user.avatar_url IS '头像URL';
COMMENT ON COLUMN sys_user.department_id IS '部门ID';
COMMENT ON COLUMN sys_user.position IS '职位';
COMMENT ON COLUMN sys_user.employee_no IS '工号';
COMMENT ON COLUMN sys_user.status IS '状态：1-正常，0-禁用，2-锁定';
```

#### 2.2.2 角色表 (sys_role)
```sql
CREATE TABLE sys_role (
    id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL,
    role_code VARCHAR(50) UNIQUE NOT NULL,
    description VARCHAR(200),
    data_scope SMALLINT DEFAULT 1, -- 1:全部 2:本部门 3:本部门及下级 4:仅本人
    status SMALLINT DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT
);

-- 添加索引
CREATE INDEX idx_role_code ON sys_role(role_code);
CREATE INDEX idx_role_status ON sys_role(status);

-- 添加注释
COMMENT ON TABLE sys_role IS '角色表';
COMMENT ON COLUMN sys_role.role_name IS '角色名称';
COMMENT ON COLUMN sys_role.role_code IS '角色编码';
COMMENT ON COLUMN sys_role.description IS '角色描述';
COMMENT ON COLUMN sys_role.data_scope IS '数据权限范围';
COMMENT ON COLUMN sys_role.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN sys_role.sort_order IS '排序';
```

#### 2.2.3 权限表 (sys_permission)
```sql
CREATE TABLE sys_permission (
    id BIGSERIAL PRIMARY KEY,
    permission_name VARCHAR(50) NOT NULL,
    permission_code VARCHAR(100) UNIQUE NOT NULL,
    permission_type SMALLINT NOT NULL, -- 1:菜单 2:按钮 3:接口
    parent_id BIGINT DEFAULT 0,
    path VARCHAR(200),
    component VARCHAR(200),
    icon VARCHAR(100),
    sort_order INTEGER DEFAULT 0,
    status SMALLINT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加索引
CREATE INDEX idx_permission_code ON sys_permission(permission_code);
CREATE INDEX idx_permission_parent ON sys_permission(parent_id);
CREATE INDEX idx_permission_type ON sys_permission(permission_type);

-- 添加注释
COMMENT ON TABLE sys_permission IS '权限表';
COMMENT ON COLUMN sys_permission.permission_name IS '权限名称';
COMMENT ON COLUMN sys_permission.permission_code IS '权限编码';
COMMENT ON COLUMN sys_permission.permission_type IS '权限类型：1-菜单，2-按钮，3-接口';
COMMENT ON COLUMN sys_permission.parent_id IS '父权限ID';
COMMENT ON COLUMN sys_permission.path IS '路由路径';
COMMENT ON COLUMN sys_permission.component IS '组件路径';
COMMENT ON COLUMN sys_permission.icon IS '图标';
```

#### 2.2.4 用户角色关联表 (sys_user_role)
```sql
CREATE TABLE sys_user_role (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    CONSTRAINT uk_user_role UNIQUE (user_id, role_id)
);

-- 添加外键约束
ALTER TABLE sys_user_role ADD CONSTRAINT fk_user_role_user
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE;
ALTER TABLE sys_user_role ADD CONSTRAINT fk_user_role_role
    FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE;

-- 添加索引
CREATE INDEX idx_user_role_user ON sys_user_role(user_id);
CREATE INDEX idx_user_role_role ON sys_user_role(role_id);

COMMENT ON TABLE sys_user_role IS '用户角色关联表';
```

#### 2.2.5 角色权限关联表 (sys_role_permission)
```sql
CREATE TABLE sys_role_permission (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_role_permission UNIQUE (role_id, permission_id)
);

-- 添加外键约束
ALTER TABLE sys_role_permission ADD CONSTRAINT fk_role_permission_role
    FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE;
ALTER TABLE sys_role_permission ADD CONSTRAINT fk_role_permission_permission
    FOREIGN KEY (permission_id) REFERENCES sys_permission(id) ON DELETE CASCADE;

-- 添加索引
CREATE INDEX idx_role_permission_role ON sys_role_permission(role_id);
CREATE INDEX idx_role_permission_permission ON sys_role_permission(permission_id);

COMMENT ON TABLE sys_role_permission IS '角色权限关联表';
```

#### 2.2.6 部门表 (sys_department)
```sql
CREATE TABLE sys_department (
    id BIGSERIAL PRIMARY KEY,
    dept_name VARCHAR(50) NOT NULL,
    dept_code VARCHAR(50) UNIQUE NOT NULL,
    parent_id BIGINT DEFAULT 0,
    dept_level INTEGER DEFAULT 1,
    dept_path VARCHAR(500),
    leader_user_id BIGINT,
    phone VARCHAR(20),
    email VARCHAR(100),
    address VARCHAR(200),
    sort_order INTEGER DEFAULT 0,
    status SMALLINT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT
);

-- 添加索引
CREATE INDEX idx_dept_code ON sys_department(dept_code);
CREATE INDEX idx_dept_parent ON sys_department(parent_id);
CREATE INDEX idx_dept_status ON sys_department(status);

-- 添加注释
COMMENT ON TABLE sys_department IS '部门表';
COMMENT ON COLUMN sys_department.dept_name IS '部门名称';
COMMENT ON COLUMN sys_department.dept_code IS '部门编码';
COMMENT ON COLUMN sys_department.parent_id IS '父部门ID';
COMMENT ON COLUMN sys_department.dept_level IS '部门层级';
COMMENT ON COLUMN sys_department.dept_path IS '部门路径';
COMMENT ON COLUMN sys_department.leader_user_id IS '部门负责人ID';
```

### 2.3 教培资源相关表

#### 2.3.1 课程体系表 (course_system)
```sql
CREATE TABLE course_system (
    id BIGSERIAL PRIMARY KEY,
    course_name VARCHAR(100) NOT NULL,
    course_code VARCHAR(50) UNIQUE NOT NULL,
    category_id BIGINT,
    course_level VARCHAR(20), -- 初级、中级、高级
    duration INTEGER, -- 课程时长(小时)
    credit_hours DECIMAL(5,2), -- 学分
    description TEXT,
    objectives TEXT, -- 学习目标
    prerequisites TEXT, -- 前置要求
    cover_image_url VARCHAR(500),
    status SMALLINT DEFAULT 1, -- 1:启用 0:禁用 2:草稿
    is_required BOOLEAN DEFAULT FALSE, -- 是否必修
    sort_order INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT
);

-- 添加索引
CREATE INDEX idx_course_code ON course_system(course_code);
CREATE INDEX idx_course_category ON course_system(category_id);
CREATE INDEX idx_course_status ON course_system(status);
CREATE INDEX idx_course_level ON course_system(course_level);

-- 添加注释
COMMENT ON TABLE course_system IS '课程体系表';
COMMENT ON COLUMN course_system.course_name IS '课程名称';
COMMENT ON COLUMN course_system.course_code IS '课程编码';
COMMENT ON COLUMN course_system.category_id IS '课程分类ID';
COMMENT ON COLUMN course_system.course_level IS '课程级别';
COMMENT ON COLUMN course_system.duration IS '课程时长(小时)';
COMMENT ON COLUMN course_system.credit_hours IS '学分';
COMMENT ON COLUMN course_system.description IS '课程描述';
COMMENT ON COLUMN course_system.objectives IS '学习目标';
COMMENT ON COLUMN course_system.prerequisites IS '前置要求';
COMMENT ON COLUMN course_system.is_required IS '是否必修课程';
```

#### 2.3.2 课程分类表 (course_category)
```sql
CREATE TABLE course_category (
    id BIGSERIAL PRIMARY KEY,
    category_name VARCHAR(50) NOT NULL,
    category_code VARCHAR(50) UNIQUE NOT NULL,
    parent_id BIGINT DEFAULT 0,
    category_level INTEGER DEFAULT 1,
    category_path VARCHAR(500),
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    status SMALLINT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加索引
CREATE INDEX idx_category_code ON course_category(category_code);
CREATE INDEX idx_category_parent ON course_category(parent_id);

COMMENT ON TABLE course_category IS '课程分类表';

#### 2.3.3 教官表 (instructor)
```sql
CREATE TABLE instructor (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    instructor_code VARCHAR(50) UNIQUE NOT NULL,
    speciality TEXT, -- 专业特长(JSON格式)
    qualification TEXT, -- 资质证书(JSON格式)
    experience_years INTEGER DEFAULT 0,
    education_background VARCHAR(100), -- 学历背景
    professional_title VARCHAR(50), -- 职称
    introduction TEXT,
    teaching_areas TEXT, -- 教学领域(JSON格式)
    contact_info JSONB, -- 联系信息
    rating DECIMAL(3,2) DEFAULT 0.00, -- 评分
    total_courses INTEGER DEFAULT 0, -- 总授课数
    total_students INTEGER DEFAULT 0, -- 总学员数
    status SMALLINT DEFAULT 1, -- 1:在职 0:离职 2:休假
    hire_date DATE,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT
);

-- 添加外键约束
ALTER TABLE instructor ADD CONSTRAINT fk_instructor_user
    FOREIGN KEY (user_id) REFERENCES sys_user(id);

-- 添加索引
CREATE INDEX idx_instructor_user ON instructor(user_id);
CREATE INDEX idx_instructor_code ON instructor(instructor_code);
CREATE INDEX idx_instructor_status ON instructor(status);

-- 添加注释
COMMENT ON TABLE instructor IS '教官表';
COMMENT ON COLUMN instructor.user_id IS '关联用户ID';
COMMENT ON COLUMN instructor.instructor_code IS '教官编号';
COMMENT ON COLUMN instructor.speciality IS '专业特长';
COMMENT ON COLUMN instructor.qualification IS '资质证书';
COMMENT ON COLUMN instructor.experience_years IS '教学经验年限';
COMMENT ON COLUMN instructor.professional_title IS '职称';
COMMENT ON COLUMN instructor.rating IS '教学评分';
```

#### 2.3.4 教案表 (teaching_material)
```sql
CREATE TABLE teaching_material (
    id BIGSERIAL PRIMARY KEY,
    material_name VARCHAR(100) NOT NULL,
    material_code VARCHAR(50) UNIQUE NOT NULL,
    course_id BIGINT NOT NULL,
    instructor_id BIGINT NOT NULL,
    chapter_no INTEGER, -- 章节号
    file_info JSONB, -- 文件信息(URL、类型、大小等)
    content_type VARCHAR(50), -- 内容类型：video、document、ppt、audio
    duration INTEGER, -- 学习时长(分钟)
    description TEXT,
    learning_objectives TEXT, -- 学习目标
    key_points TEXT, -- 重点内容
    difficulty_level VARCHAR(20), -- 难度级别
    version VARCHAR(20) DEFAULT '1.0',
    status SMALLINT DEFAULT 1, -- 1:启用 0:禁用 2:审核中
    view_count INTEGER DEFAULT 0, -- 浏览次数
    download_count INTEGER DEFAULT 0, -- 下载次数
    approval_status SMALLINT DEFAULT 0, -- 0:待审核 1:已审核 2:审核拒绝
    approval_user_id BIGINT,
    approval_time TIMESTAMP,
    approval_comment TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT
);

-- 添加外键约束
ALTER TABLE teaching_material ADD CONSTRAINT fk_material_course
    FOREIGN KEY (course_id) REFERENCES course_system(id);
ALTER TABLE teaching_material ADD CONSTRAINT fk_material_instructor
    FOREIGN KEY (instructor_id) REFERENCES instructor(id);

-- 添加索引
CREATE INDEX idx_material_course ON teaching_material(course_id);
CREATE INDEX idx_material_instructor ON teaching_material(instructor_id);
CREATE INDEX idx_material_status ON teaching_material(status);
CREATE INDEX idx_material_type ON teaching_material(content_type);

-- 添加注释
COMMENT ON TABLE teaching_material IS '教案表';
COMMENT ON COLUMN teaching_material.material_name IS '教案名称';
COMMENT ON COLUMN teaching_material.material_code IS '教案编码';
COMMENT ON COLUMN teaching_material.course_id IS '关联课程ID';
COMMENT ON COLUMN teaching_material.instructor_id IS '教官ID';
COMMENT ON COLUMN teaching_material.file_info IS '文件信息';
COMMENT ON COLUMN teaching_material.content_type IS '内容类型';
COMMENT ON COLUMN teaching_material.duration IS '学习时长(分钟)';
```

#### 2.3.5 考题表 (exam_question)
```sql
CREATE TABLE exam_question (
    id BIGSERIAL PRIMARY KEY,
    question_text TEXT NOT NULL,
    question_type VARCHAR(20) NOT NULL, -- single_choice、multiple_choice、true_false、fill_blank、essay
    course_id BIGINT,
    category_id BIGINT, -- 题目分类
    difficulty VARCHAR(20) DEFAULT 'medium', -- easy、medium、hard
    score DECIMAL(5,2) DEFAULT 1.00,
    options JSONB, -- 选项内容(JSON格式)
    correct_answer TEXT, -- 正确答案
    answer_analysis TEXT, -- 答案解析
    knowledge_points TEXT, -- 知识点
    tags TEXT, -- 标签
    usage_count INTEGER DEFAULT 0, -- 使用次数
    correct_rate DECIMAL(5,2) DEFAULT 0.00, -- 正确率
    status SMALLINT DEFAULT 1, -- 1:启用 0:禁用 2:审核中
    approval_status SMALLINT DEFAULT 0,
    approval_user_id BIGINT,
    approval_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT
);

-- 添加外键约束
ALTER TABLE exam_question ADD CONSTRAINT fk_question_course
    FOREIGN KEY (course_id) REFERENCES course_system(id);

-- 添加索引
CREATE INDEX idx_question_course ON exam_question(course_id);
CREATE INDEX idx_question_type ON exam_question(question_type);
CREATE INDEX idx_question_difficulty ON exam_question(difficulty);
CREATE INDEX idx_question_status ON exam_question(status);

-- 添加注释
COMMENT ON TABLE exam_question IS '考题表';
COMMENT ON COLUMN exam_question.question_text IS '题目内容';
COMMENT ON COLUMN exam_question.question_type IS '题目类型';
COMMENT ON COLUMN exam_question.difficulty IS '难度级别';
COMMENT ON COLUMN exam_question.options IS '选项内容';
COMMENT ON COLUMN exam_question.correct_answer IS '正确答案';
COMMENT ON COLUMN exam_question.answer_analysis IS '答案解析';
```

### 2.4 教培计划相关表

#### 2.4.1 培训计划表 (training_plan)
```sql
CREATE TABLE training_plan (
    id BIGSERIAL PRIMARY KEY,
    plan_name VARCHAR(100) NOT NULL,
    plan_code VARCHAR(50) UNIQUE NOT NULL,
    plan_type VARCHAR(20) DEFAULT 'regular', -- regular、special、emergency
    training_objective TEXT,
    target_audience TEXT, -- 目标受众
    start_date DATE,
    end_date DATE,
    registration_start_date DATE, -- 报名开始时间
    registration_end_date DATE, -- 报名结束时间
    total_hours INTEGER DEFAULT 0,
    max_participants INTEGER, -- 最大参与人数
    min_participants INTEGER, -- 最小参与人数
    organizer_dept_id BIGINT, -- 主办部门ID
    contact_person_id BIGINT, -- 联系人ID
    location VARCHAR(200), -- 培训地点
    training_mode VARCHAR(20) DEFAULT 'online', -- online、offline、hybrid
    requirements TEXT, -- 参训要求
    schedule_info JSONB, -- 日程安排
    budget DECIMAL(10,2), -- 预算
    status VARCHAR(20) DEFAULT 'DRAFT', -- DRAFT、PENDING、APPROVED、PUBLISHED、ONGOING、COMPLETED、CANCELLED
    approval_status VARCHAR(20) DEFAULT 'PENDING', -- PENDING、APPROVED、REJECTED
    approval_user_id BIGINT,
    approval_time TIMESTAMP,
    approval_comment TEXT,
    publish_time TIMESTAMP,
    completion_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT
);

-- 添加索引
CREATE INDEX idx_plan_code ON training_plan(plan_code);
CREATE INDEX idx_plan_status ON training_plan(status);
CREATE INDEX idx_plan_organizer ON training_plan(organizer_dept_id);
CREATE INDEX idx_plan_date ON training_plan(start_date, end_date);
CREATE INDEX idx_plan_approval ON training_plan(approval_status);

-- 添加注释
COMMENT ON TABLE training_plan IS '培训计划表';
COMMENT ON COLUMN training_plan.plan_name IS '计划名称';
COMMENT ON COLUMN training_plan.plan_code IS '计划编号';
COMMENT ON COLUMN training_plan.plan_type IS '计划类型';
COMMENT ON COLUMN training_plan.training_objective IS '培训目标';
COMMENT ON COLUMN training_plan.organizer_dept_id IS '主办部门ID';
COMMENT ON COLUMN training_plan.training_mode IS '培训模式';
```

#### 2.4.2 计划课程关联表 (plan_course)
```sql
CREATE TABLE plan_course (
    id BIGSERIAL PRIMARY KEY,
    plan_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    instructor_id BIGINT,
    sequence_no INTEGER DEFAULT 1, -- 课程顺序
    planned_hours INTEGER DEFAULT 0, -- 计划学时
    actual_hours INTEGER DEFAULT 0, -- 实际学时
    start_date DATE,
    end_date DATE,
    location VARCHAR(200),
    teaching_method VARCHAR(50), -- 教学方式
    requirements TEXT, -- 特殊要求
    status VARCHAR(20) DEFAULT 'PLANNED', -- PLANNED、ONGOING、COMPLETED、CANCELLED
    completion_rate DECIMAL(5,2) DEFAULT 0.00, -- 完成率
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加外键约束
ALTER TABLE plan_course ADD CONSTRAINT fk_plan_course_plan
    FOREIGN KEY (plan_id) REFERENCES training_plan(id) ON DELETE CASCADE;
ALTER TABLE plan_course ADD CONSTRAINT fk_plan_course_course
    FOREIGN KEY (course_id) REFERENCES course_system(id);
ALTER TABLE plan_course ADD CONSTRAINT fk_plan_course_instructor
    FOREIGN KEY (instructor_id) REFERENCES instructor(id);

-- 添加索引
CREATE INDEX idx_plan_course_plan ON plan_course(plan_id);
CREATE INDEX idx_plan_course_course ON plan_course(course_id);
CREATE INDEX idx_plan_course_instructor ON plan_course(instructor_id);

COMMENT ON TABLE plan_course IS '计划课程关联表';
```

#### 2.4.3 计划参训人员表 (plan_trainee)
```sql
CREATE TABLE plan_trainee (
    id BIGSERIAL PRIMARY KEY,
    plan_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    enrollment_status VARCHAR(20) DEFAULT 'ENROLLED', -- ENROLLED、CONFIRMED、COMPLETED、DROPPED、ABSENT
    enrollment_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    confirmation_time TIMESTAMP,
    completion_time TIMESTAMP,
    attendance_rate DECIMAL(5,2) DEFAULT 0.00, -- 出勤率
    final_score DECIMAL(5,2), -- 最终成绩
    certificate_issued BOOLEAN DEFAULT FALSE, -- 是否已颁发证书
    certificate_no VARCHAR(100), -- 证书编号
    feedback TEXT, -- 培训反馈
    rating INTEGER, -- 评分(1-5)
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_plan_trainee UNIQUE (plan_id, user_id)
);

-- 添加外键约束
ALTER TABLE plan_trainee ADD CONSTRAINT fk_plan_trainee_plan
    FOREIGN KEY (plan_id) REFERENCES training_plan(id) ON DELETE CASCADE;
ALTER TABLE plan_trainee ADD CONSTRAINT fk_plan_trainee_user
    FOREIGN KEY (user_id) REFERENCES sys_user(id);

-- 添加索引
CREATE INDEX idx_plan_trainee_plan ON plan_trainee(plan_id);
CREATE INDEX idx_plan_trainee_user ON plan_trainee(user_id);
CREATE INDEX idx_plan_trainee_status ON plan_trainee(enrollment_status);

COMMENT ON TABLE plan_trainee IS '计划参训人员表';
```

### 2.5 学习管理相关表

#### 2.5.1 学习任务表 (learning_task)
```sql
CREATE TABLE learning_task (
    id BIGSERIAL PRIMARY KEY,
    task_name VARCHAR(100) NOT NULL,
    task_code VARCHAR(50) UNIQUE NOT NULL,
    plan_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    material_id BIGINT,
    user_id BIGINT NOT NULL,
    task_type VARCHAR(20) DEFAULT 'study', -- study、assignment、discussion
    required_duration INTEGER DEFAULT 0, -- 要求学习时长(分钟)
    actual_duration INTEGER DEFAULT 0, -- 实际学习时长(分钟)
    progress DECIMAL(5,2) DEFAULT 0.00, -- 学习进度(百分比)
    status VARCHAR(20) DEFAULT 'NOT_STARTED', -- NOT_STARTED、IN_PROGRESS、COMPLETED、OVERDUE
    start_time TIMESTAMP,
    complete_time TIMESTAMP,
    deadline TIMESTAMP,
    priority INTEGER DEFAULT 1, -- 优先级
    description TEXT,
    learning_notes TEXT, -- 学习笔记
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加外键约束
ALTER TABLE learning_task ADD CONSTRAINT fk_learning_task_plan
    FOREIGN KEY (plan_id) REFERENCES training_plan(id);
ALTER TABLE learning_task ADD CONSTRAINT fk_learning_task_course
    FOREIGN KEY (course_id) REFERENCES course_system(id);
ALTER TABLE learning_task ADD CONSTRAINT fk_learning_task_material
    FOREIGN KEY (material_id) REFERENCES teaching_material(id);
ALTER TABLE learning_task ADD CONSTRAINT fk_learning_task_user
    FOREIGN KEY (user_id) REFERENCES sys_user(id);

-- 添加索引
CREATE INDEX idx_learning_task_plan ON learning_task(plan_id);
CREATE INDEX idx_learning_task_user ON learning_task(user_id);
CREATE INDEX idx_learning_task_status ON learning_task(status);
CREATE INDEX idx_learning_task_deadline ON learning_task(deadline);

COMMENT ON TABLE learning_task IS '学习任务表';
```

#### 2.5.2 学习进度表 (learning_progress)
```sql
CREATE TABLE learning_progress (
    id BIGSERIAL PRIMARY KEY,
    task_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    session_id VARCHAR(100), -- 会话ID
    progress_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    current_position INTEGER DEFAULT 0, -- 当前位置(秒)
    duration INTEGER DEFAULT 0, -- 本次学习时长(秒)
    device_info JSONB, -- 设备信息
    ip_address VARCHAR(50),
    user_agent TEXT,
    action_type VARCHAR(20), -- start、pause、resume、complete、bookmark
    action_data JSONB, -- 行为数据
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加外键约束
ALTER TABLE learning_progress ADD CONSTRAINT fk_learning_progress_task
    FOREIGN KEY (task_id) REFERENCES learning_task(id) ON DELETE CASCADE;
ALTER TABLE learning_progress ADD CONSTRAINT fk_learning_progress_user
    FOREIGN KEY (user_id) REFERENCES sys_user(id);

-- 添加索引
CREATE INDEX idx_learning_progress_task ON learning_progress(task_id);
CREATE INDEX idx_learning_progress_user ON learning_progress(user_id);
CREATE INDEX idx_learning_progress_time ON learning_progress(progress_time);

COMMENT ON TABLE learning_progress IS '学习进度表';
```

### 2.6 考试管理相关表

#### 2.6.1 考试表 (exam)
```sql
CREATE TABLE exam (
    id BIGSERIAL PRIMARY KEY,
    exam_name VARCHAR(100) NOT NULL,
    exam_code VARCHAR(50) UNIQUE NOT NULL,
    plan_id BIGINT,
    course_id BIGINT,
    exam_type VARCHAR(20) DEFAULT 'final', -- final、midterm、quiz、practice
    description TEXT,
    instructions TEXT, -- 考试说明
    total_score DECIMAL(5,2) DEFAULT 100.00,
    pass_score DECIMAL(5,2) DEFAULT 60.00,
    duration INTEGER NOT NULL, -- 考试时长(分钟)
    question_count INTEGER DEFAULT 0, -- 题目数量
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    late_submission_allowed BOOLEAN DEFAULT FALSE, -- 是否允许迟交
    late_penalty DECIMAL(5,2) DEFAULT 0.00, -- 迟交扣分
    shuffle_questions BOOLEAN DEFAULT TRUE, -- 是否打乱题目顺序
    shuffle_options BOOLEAN DEFAULT TRUE, -- 是否打乱选项顺序
    show_result_immediately BOOLEAN DEFAULT FALSE, -- 是否立即显示结果
    allow_review BOOLEAN DEFAULT TRUE, -- 是否允许查看答案
    max_attempts INTEGER DEFAULT 1, -- 最大尝试次数
    status VARCHAR(20) DEFAULT 'DRAFT', -- DRAFT、PUBLISHED、ONGOING、COMPLETED、CANCELLED
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT
);

-- 添加外键约束
ALTER TABLE exam ADD CONSTRAINT fk_exam_plan
    FOREIGN KEY (plan_id) REFERENCES training_plan(id);
ALTER TABLE exam ADD CONSTRAINT fk_exam_course
    FOREIGN KEY (course_id) REFERENCES course_system(id);

-- 添加索引
CREATE INDEX idx_exam_code ON exam(exam_code);
CREATE INDEX idx_exam_plan ON exam(plan_id);
CREATE INDEX idx_exam_course ON exam(course_id);
CREATE INDEX idx_exam_status ON exam(status);
CREATE INDEX idx_exam_time ON exam(start_time, end_time);

COMMENT ON TABLE exam IS '考试表';
```

#### 2.6.2 试卷表 (exam_paper)
```sql
CREATE TABLE exam_paper (
    id BIGSERIAL PRIMARY KEY,
    paper_code VARCHAR(50) UNIQUE NOT NULL,
    exam_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    paper_content JSONB NOT NULL, -- 试卷内容(题目和选项)
    answer_sheet JSONB, -- 答题卡
    total_score DECIMAL(5,2) DEFAULT 0.00,
    actual_score DECIMAL(5,2) DEFAULT 0.00,
    correct_count INTEGER DEFAULT 0,
    wrong_count INTEGER DEFAULT 0,
    unanswered_count INTEGER DEFAULT 0,
    start_time TIMESTAMP,
    submit_time TIMESTAMP,
    duration INTEGER DEFAULT 0, -- 实际用时(分钟)
    attempt_number INTEGER DEFAULT 1, -- 尝试次数
    status VARCHAR(20) DEFAULT 'NOT_STARTED', -- NOT_STARTED、IN_PROGRESS、SUBMITTED、GRADED、EXPIRED
    ip_address VARCHAR(50),
    user_agent TEXT,
    device_info JSONB,
    anti_cheat_data JSONB, -- 防作弊数据
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加外键约束
ALTER TABLE exam_paper ADD CONSTRAINT fk_exam_paper_exam
    FOREIGN KEY (exam_id) REFERENCES exam(id);
ALTER TABLE exam_paper ADD CONSTRAINT fk_exam_paper_user
    FOREIGN KEY (user_id) REFERENCES sys_user(id);

-- 添加索引
CREATE INDEX idx_exam_paper_exam ON exam_paper(exam_id);
CREATE INDEX idx_exam_paper_user ON exam_paper(user_id);
CREATE INDEX idx_exam_paper_status ON exam_paper(status);
CREATE INDEX idx_exam_paper_code ON exam_paper(paper_code);

COMMENT ON TABLE exam_paper IS '试卷表';
```

#### 2.6.3 考试题目关联表 (exam_question_rel)
```sql
CREATE TABLE exam_question_rel (
    id BIGSERIAL PRIMARY KEY,
    exam_id BIGINT NOT NULL,
    question_id BIGINT NOT NULL,
    question_order INTEGER DEFAULT 1, -- 题目顺序
    question_score DECIMAL(5,2) DEFAULT 1.00, -- 题目分值
    is_required BOOLEAN DEFAULT TRUE, -- 是否必答
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加外键约束
ALTER TABLE exam_question_rel ADD CONSTRAINT fk_exam_question_exam
    FOREIGN KEY (exam_id) REFERENCES exam(id) ON DELETE CASCADE;
ALTER TABLE exam_question_rel ADD CONSTRAINT fk_exam_question_question
    FOREIGN KEY (question_id) REFERENCES exam_question(id);

-- 添加索引
CREATE INDEX idx_exam_question_exam ON exam_question_rel(exam_id);
CREATE INDEX idx_exam_question_question ON exam_question_rel(question_id);
CREATE INDEX idx_exam_question_order ON exam_question_rel(exam_id, question_order);

COMMENT ON TABLE exam_question_rel IS '考试题目关联表';
```

#### 2.6.4 考试结果表 (exam_result)
```sql
CREATE TABLE exam_result (
    id BIGSERIAL PRIMARY KEY,
    exam_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    paper_id BIGINT NOT NULL,
    total_score DECIMAL(5,2) DEFAULT 0.00,
    actual_score DECIMAL(5,2) DEFAULT 0.00,
    percentage DECIMAL(5,2) DEFAULT 0.00, -- 得分率
    grade VARCHAR(20), -- 等级：优秀、良好、合格、不合格
    is_passed BOOLEAN DEFAULT FALSE,
    correct_count INTEGER DEFAULT 0,
    wrong_count INTEGER DEFAULT 0,
    unanswered_count INTEGER DEFAULT 0,
    duration INTEGER DEFAULT 0, -- 用时(分钟)
    ranking INTEGER, -- 排名
    percentile DECIMAL(5,2), -- 百分位
    detailed_result JSONB, -- 详细结果(每题得分情况)
    feedback TEXT, -- 反馈意见
    certificate_eligible BOOLEAN DEFAULT FALSE, -- 是否符合证书条件
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加外键约束
ALTER TABLE exam_result ADD CONSTRAINT fk_exam_result_exam
    FOREIGN KEY (exam_id) REFERENCES exam(id);
ALTER TABLE exam_result ADD CONSTRAINT fk_exam_result_user
    FOREIGN KEY (user_id) REFERENCES sys_user(id);
ALTER TABLE exam_result ADD CONSTRAINT fk_exam_result_paper
    FOREIGN KEY (paper_id) REFERENCES exam_paper(id);

-- 添加索引
CREATE INDEX idx_exam_result_exam ON exam_result(exam_id);
CREATE INDEX idx_exam_result_user ON exam_result(user_id);
CREATE INDEX idx_exam_result_score ON exam_result(actual_score);
CREATE INDEX idx_exam_result_grade ON exam_result(grade);

COMMENT ON TABLE exam_result IS '考试结果表';
```

### 2.7 文件管理相关表

#### 2.7.1 文件信息表 (file_info)
```sql
CREATE TABLE file_info (
    id BIGSERIAL PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(50),
    mime_type VARCHAR(100),
    file_hash VARCHAR(64), -- 文件哈希值
    bucket_name VARCHAR(100), -- MinIO桶名
    object_name VARCHAR(500), -- MinIO对象名
    file_url VARCHAR(500), -- 访问URL
    thumbnail_url VARCHAR(500), -- 缩略图URL
    business_type VARCHAR(50), -- 业务类型：avatar、material、document等
    business_id BIGINT, -- 业务ID
    download_count INTEGER DEFAULT 0,
    status SMALLINT DEFAULT 1, -- 1:正常 0:删除
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT
);

-- 添加索引
CREATE INDEX idx_file_hash ON file_info(file_hash);
CREATE INDEX idx_file_business ON file_info(business_type, business_id);
CREATE INDEX idx_file_status ON file_info(status);
CREATE INDEX idx_file_type ON file_info(file_type);

COMMENT ON TABLE file_info IS '文件信息表';
```

### 2.8 系统管理相关表

#### 2.8.1 系统配置表 (sys_config)
```sql
CREATE TABLE sys_config (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string', -- string、number、boolean、json
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE, -- 是否系统配置
    sort_order INTEGER DEFAULT 0,
    status SMALLINT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT
);

-- 添加索引
CREATE INDEX idx_config_key ON sys_config(config_key);
CREATE INDEX idx_config_status ON sys_config(status);

COMMENT ON TABLE sys_config IS '系统配置表';
```

#### 2.8.2 操作日志表 (sys_log)
```sql
CREATE TABLE sys_log (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT,
    username VARCHAR(50),
    operation VARCHAR(100), -- 操作描述
    method VARCHAR(10), -- HTTP方法
    request_url VARCHAR(500),
    request_params TEXT,
    response_data TEXT,
    ip_address VARCHAR(50),
    user_agent TEXT,
    execution_time INTEGER, -- 执行时间(毫秒)
    status VARCHAR(10), -- SUCCESS、FAILURE
    error_message TEXT,
    module VARCHAR(50), -- 模块名称
    business_type VARCHAR(50), -- 业务类型
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加索引
CREATE INDEX idx_log_user ON sys_log(user_id);
CREATE INDEX idx_log_time ON sys_log(create_time);
CREATE INDEX idx_log_module ON sys_log(module);
CREATE INDEX idx_log_status ON sys_log(status);

COMMENT ON TABLE sys_log IS '操作日志表';
```
