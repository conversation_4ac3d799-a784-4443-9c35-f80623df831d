<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统首页 - 教育培训系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
            transition: width 0.3s ease;
        }
        
        .sidebar.collapsed {
            width: 64px;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 32px;
            height: 32px;
            background: #3498db;
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .sidebar.collapsed .sidebar-title {
            display: none;
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
        }
        
        .menu-item:hover {
            background: #34495e;
        }
        
        .menu-item.active {
            background: #3498db;
        }
        
        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }
        
        .sidebar.collapsed .menu-text {
            display: none;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .toggle-btn {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            margin-right: 20px;
            padding: 8px;
            border-radius: 4px;
        }
        
        .toggle-btn:hover {
            background: #f0f0f0;
        }
        
        .breadcrumb {
            color: #666;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-right: 20px;
        }
        
        .stat-icon.blue { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.green { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.orange { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-icon.purple { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        
        .stat-info h3 {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-info p {
            color: #666;
            font-size: 14px;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }
        
        .chart-placeholder {
            height: 300px;
            background: #f8f9fa;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }
        
        .recent-activities {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e3f2fd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: #2196f3;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">T</div>
                <div class="sidebar-title">培训系统</div>
            </div>
            <div class="sidebar-menu">
                <div class="menu-item active">
                    <div class="menu-icon">📊</div>
                    <div class="menu-text">系统首页</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../user/user-list.html')">
                    <div class="menu-icon">👥</div>
                    <div class="menu-text">用户管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../course/course-list.html')">
                    <div class="menu-icon">📚</div>
                    <div class="menu-text">课程管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../instructor/instructor-list.html')">
                    <div class="menu-icon">👨‍🏫</div>
                    <div class="menu-text">教官管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../plan/plan-list.html')">
                    <div class="menu-icon">📋</div>
                    <div class="menu-text">培训计划</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../learning/learning-dashboard.html')">
                    <div class="menu-icon">🎓</div>
                    <div class="menu-text">在线学习</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../exam/exam-list.html')">
                    <div class="menu-icon">📝</div>
                    <div class="menu-text">在线考试</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../system/system-config.html')">
                    <div class="menu-icon">⚙️</div>
                    <div class="menu-text">系统设置</div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="header-left">
                    <button class="toggle-btn" onclick="toggleSidebar()">☰</button>
                    <div class="breadcrumb">首页 / 系统概览</div>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <div class="avatar">管</div>
                        <span>管理员</span>
                        <button onclick="logout()" style="margin-left: 10px; padding: 5px 10px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer;">退出</button>
                    </div>
                </div>
            </div>
            
            <div class="content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon blue">👥</div>
                        <div class="stat-info">
                            <h3>1,234</h3>
                            <p>注册用户总数</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon green">📚</div>
                        <div class="stat-info">
                            <h3>156</h3>
                            <p>课程总数</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon orange">🎓</div>
                        <div class="stat-info">
                            <h3>89</h3>
                            <p>进行中的培训</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon purple">📝</div>
                        <div class="stat-info">
                            <h3>2,567</h3>
                            <p>已完成考试</p>
                        </div>
                    </div>
                </div>
                
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">培训完成趋势</div>
                            <select style="padding: 5px; border: 1px solid #ddd; border-radius: 4px;">
                                <option>最近7天</option>
                                <option>最近30天</option>
                                <option>最近3个月</option>
                            </select>
                        </div>
                        <div class="chart-placeholder">
                            📈 培训完成趋势图表
                        </div>
                    </div>
                    
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">课程分类分布</div>
                        </div>
                        <div class="chart-placeholder">
                            🥧 课程分类饼图
                        </div>
                    </div>
                </div>
                
                <div class="recent-activities">
                    <div class="chart-header">
                        <div class="chart-title">最近活动</div>
                        <a href="#" style="color: #3498db; text-decoration: none;">查看全部</a>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">📚</div>
                        <div class="activity-content">
                            <div class="activity-title">新课程《Java高级编程》已发布</div>
                            <div class="activity-time">2小时前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">👥</div>
                        <div class="activity-content">
                            <div class="activity-title">用户张三完成了《Spring Boot实战》培训</div>
                            <div class="activity-time">4小时前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">📝</div>
                        <div class="activity-content">
                            <div class="activity-title">考试《Java基础测试》已开始</div>
                            <div class="activity-time">6小时前</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">📋</div>
                        <div class="activity-content">
                            <div class="activity-title">培训计划《2024年技术培训》已审核通过</div>
                            <div class="activity-time">1天前</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed');
        }
        
        function navigateTo(url) {
            window.location.href = url;
        }
        
        function logout() {
            if (confirm('确定要退出系统吗？')) {
                window.location.href = '../auth/login.html';
            }
        }
        
        // 模拟实时数据更新
        function updateStats() {
            const stats = document.querySelectorAll('.stat-info h3');
            stats.forEach(stat => {
                const currentValue = parseInt(stat.textContent.replace(',', ''));
                const newValue = currentValue + Math.floor(Math.random() * 5);
                stat.textContent = newValue.toLocaleString();
            });
        }
        
        // 每30秒更新一次数据
        setInterval(updateStats, 30000);
    </script>
</body>
</html>
