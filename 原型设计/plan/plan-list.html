<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>培训计划 - 教育培训系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 32px;
            height: 32px;
            background: #3498db;
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
        }
        
        .menu-item:hover {
            background: #34495e;
        }
        
        .menu-item.active {
            background: #3498db;
        }
        
        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .breadcrumb {
            color: #666;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }
        
        .page-description {
            color: #666;
        }
        
        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .toolbar-left {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .toolbar-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-default {
            background: #ecf0f1;
            color: #333;
            border: 1px solid #bdc3c7;
        }
        
        .btn-default:hover {
            background: #d5dbdb;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
        }
        
        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .plan-list {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .plan-item {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.3s ease;
        }
        
        .plan-item:hover {
            background: #f8f9fa;
        }
        
        .plan-item:last-child {
            border-bottom: none;
        }
        
        .plan-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .plan-title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }
        
        .plan-code {
            font-size: 14px;
            color: #666;
        }
        
        .plan-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-draft {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-pending {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-approved {
            background: #d4edda;
            color: #155724;
        }
        
        .status-published {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-ongoing {
            background: #e2e3e5;
            color: #383d41;
        }
        
        .status-completed {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .plan-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }
        
        .meta-icon {
            width: 16px;
            text-align: center;
        }
        
        .plan-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .plan-progress {
            margin-bottom: 15px;
        }
        
        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            transition: width 0.3s ease;
        }
        
        .plan-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-warning:hover {
            background: #e67e22;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .pagination {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            color: #333;
        }
        
        .page-btn:hover {
            background: #f0f0f0;
        }
        
        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .empty-text {
            font-size: 16px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">T</div>
                <div class="sidebar-title">培训系统</div>
            </div>
            <div class="sidebar-menu">
                <div class="menu-item" onclick="navigateTo('../dashboard/dashboard.html')">
                    <div class="menu-icon">📊</div>
                    <div class="menu-text">系统首页</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../user/user-list.html')">
                    <div class="menu-icon">👥</div>
                    <div class="menu-text">用户管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../course/course-list.html')">
                    <div class="menu-icon">📚</div>
                    <div class="menu-text">课程管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../instructor/instructor-list.html')">
                    <div class="menu-icon">👨‍🏫</div>
                    <div class="menu-text">教官管理</div>
                </div>
                <div class="menu-item active">
                    <div class="menu-icon">📋</div>
                    <div class="menu-text">培训计划</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../learning/learning-dashboard.html')">
                    <div class="menu-icon">🎓</div>
                    <div class="menu-text">在线学习</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../exam/exam-list.html')">
                    <div class="menu-icon">📝</div>
                    <div class="menu-text">在线考试</div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="breadcrumb">首页 / 培训计划 / 计划列表</div>
                <div class="user-info">
                    <div class="avatar">管</div>
                    <span>管理员</span>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">培训计划</h1>
                    <p class="page-description">管理培训计划的创建、审核、发布和执行全流程</p>
                </div>
                
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="createPlan()">+ 创建计划</button>
                        <button class="btn btn-default" onclick="exportPlans()">导出数据</button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <select class="filter-select">
                                <option value="">全部状态</option>
                                <option value="DRAFT">草稿</option>
                                <option value="PENDING">待审核</option>
                                <option value="APPROVED">已审核</option>
                                <option value="PUBLISHED">已发布</option>
                                <option value="ONGOING">进行中</option>
                                <option value="COMPLETED">已完成</option>
                            </select>
                            <input type="text" class="search-input" placeholder="搜索计划名称">
                            <button class="btn btn-primary">搜索</button>
                        </div>
                    </div>
                </div>
                
                <div class="plan-list" id="planList">
                    <!-- 培训计划列表将通过JavaScript动态生成 -->
                </div>
                
                <div class="pagination">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟培训计划数据
        const plans = [
            {
                id: 1,
                planName: '2024年Java开发培训计划',
                planCode: 'PLAN_2024_JAVA_001',
                status: 'PUBLISHED',
                startDate: '2024-03-01',
                endDate: '2024-03-31',
                totalHours: 80,
                maxParticipants: 30,
                currentParticipants: 25,
                organizer: '技术部',
                description: '面向初级开发人员的Java基础培训，包含语法基础、面向对象编程、常用框架等内容',
                progress: 65,
                courseCount: 8
            },
            {
                id: 2,
                planName: '前端技术提升培训',
                planCode: 'PLAN_2024_FE_001',
                status: 'ONGOING',
                startDate: '2024-02-15',
                endDate: '2024-03-15',
                totalHours: 60,
                maxParticipants: 25,
                currentParticipants: 22,
                organizer: '技术部',
                description: 'Vue.js、React等现代前端框架的深入学习和实战项目开发',
                progress: 80,
                courseCount: 6
            },
            {
                id: 3,
                planName: '数据库管理培训',
                planCode: 'PLAN_2024_DB_001',
                status: 'APPROVED',
                startDate: '2024-04-01',
                endDate: '2024-04-30',
                totalHours: 40,
                maxParticipants: 20,
                currentParticipants: 0,
                organizer: '技术部',
                description: 'MySQL、PostgreSQL数据库设计、优化和管理最佳实践',
                progress: 0,
                courseCount: 5
            },
            {
                id: 4,
                planName: '项目管理培训',
                planCode: 'PLAN_2024_PM_001',
                status: 'PENDING',
                startDate: '2024-05-01',
                endDate: '2024-05-31',
                totalHours: 32,
                maxParticipants: 15,
                currentParticipants: 0,
                organizer: '人事部',
                description: '敏捷开发、项目管理工具和团队协作方法论培训',
                progress: 0,
                courseCount: 4
            },
            {
                id: 5,
                planName: '新员工入职培训',
                planCode: 'PLAN_2024_NEW_001',
                status: 'COMPLETED',
                startDate: '2024-01-15',
                endDate: '2024-02-15',
                totalHours: 24,
                maxParticipants: 50,
                currentParticipants: 48,
                organizer: '人事部',
                description: '公司文化、制度流程、基础技能等新员工必修课程',
                progress: 100,
                courseCount: 3
            }
        ];
        
        function getStatusText(status) {
            const statusMap = {
                'DRAFT': '草稿',
                'PENDING': '待审核',
                'APPROVED': '已审核',
                'PUBLISHED': '已发布',
                'ONGOING': '进行中',
                'COMPLETED': '已完成'
            };
            return statusMap[status] || status;
        }
        
        function renderPlanList() {
            const container = document.getElementById('planList');
            container.innerHTML = '';
            
            if (plans.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📋</div>
                        <div class="empty-text">暂无培训计划</div>
                        <button class="btn btn-primary" onclick="createPlan()">创建第一个计划</button>
                    </div>
                `;
                return;
            }
            
            plans.forEach(plan => {
                const planItem = document.createElement('div');
                planItem.className = 'plan-item';
                
                const actions = getActionsForStatus(plan.status, plan.id);
                
                planItem.innerHTML = `
                    <div class="plan-header">
                        <div>
                            <div class="plan-title">${plan.planName}</div>
                            <div class="plan-code">${plan.planCode}</div>
                        </div>
                        <div class="plan-status status-${plan.status.toLowerCase()}">${getStatusText(plan.status)}</div>
                    </div>
                    
                    <div class="plan-meta">
                        <div class="meta-item">
                            <span class="meta-icon">📅</span>
                            <span>${plan.startDate} 至 ${plan.endDate}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">⏱️</span>
                            <span>${plan.totalHours}学时</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">👥</span>
                            <span>${plan.currentParticipants}/${plan.maxParticipants}人</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">📚</span>
                            <span>${plan.courseCount}门课程</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">🏢</span>
                            <span>${plan.organizer}</span>
                        </div>
                    </div>
                    
                    <div class="plan-description">${plan.description}</div>
                    
                    <div class="plan-progress">
                        <div class="progress-label">
                            <span>完成进度</span>
                            <span>${plan.progress}%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${plan.progress}%"></div>
                        </div>
                    </div>
                    
                    <div class="plan-actions">
                        ${actions}
                    </div>
                `;
                
                container.appendChild(planItem);
            });
        }
        
        function getActionsForStatus(status, planId) {
            const baseActions = `<button class="btn btn-info btn-sm" onclick="viewPlan(${planId})">查看详情</button>`;
            
            switch (status) {
                case 'DRAFT':
                    return baseActions + `
                        <button class="btn btn-warning btn-sm" onclick="editPlan(${planId})">编辑</button>
                        <button class="btn btn-success btn-sm" onclick="submitPlan(${planId})">提交审核</button>
                        <button class="btn btn-danger btn-sm" onclick="deletePlan(${planId})">删除</button>
                    `;
                case 'PENDING':
                    return baseActions + `
                        <button class="btn btn-success btn-sm" onclick="approvePlan(${planId})">审核通过</button>
                        <button class="btn btn-danger btn-sm" onclick="rejectPlan(${planId})">审核拒绝</button>
                    `;
                case 'APPROVED':
                    return baseActions + `
                        <button class="btn btn-success btn-sm" onclick="publishPlan(${planId})">发布计划</button>
                        <button class="btn btn-warning btn-sm" onclick="editPlan(${planId})">编辑</button>
                    `;
                case 'PUBLISHED':
                    return baseActions + `
                        <button class="btn btn-success btn-sm" onclick="startPlan(${planId})">开始执行</button>
                        <button class="btn btn-info btn-sm" onclick="manageTrainees(${planId})">管理学员</button>
                    `;
                case 'ONGOING':
                    return baseActions + `
                        <button class="btn btn-info btn-sm" onclick="viewProgress(${planId})">查看进度</button>
                        <button class="btn btn-info btn-sm" onclick="manageTrainees(${planId})">管理学员</button>
                        <button class="btn btn-success btn-sm" onclick="completePlan(${planId})">完成计划</button>
                    `;
                case 'COMPLETED':
                    return baseActions + `
                        <button class="btn btn-info btn-sm" onclick="viewReport(${planId})">查看报告</button>
                        <button class="btn btn-default btn-sm" onclick="exportReport(${planId})">导出报告</button>
                    `;
                default:
                    return baseActions;
            }
        }
        
        function navigateTo(url) {
            window.location.href = url;
        }
        
        function createPlan() {
            alert('创建培训计划功能开发中...');
        }
        
        function viewPlan(id) {
            alert(`查看计划详情 - ID: ${id}`);
        }
        
        function editPlan(id) {
            alert(`编辑培训计划 - ID: ${id}`);
        }
        
        function submitPlan(id) {
            if (confirm('确定要提交审核吗？')) {
                const plan = plans.find(p => p.id === id);
                if (plan) {
                    plan.status = 'PENDING';
                    renderPlanList();
                    alert('计划已提交审核！');
                }
            }
        }
        
        function approvePlan(id) {
            if (confirm('确定要审核通过吗？')) {
                const plan = plans.find(p => p.id === id);
                if (plan) {
                    plan.status = 'APPROVED';
                    renderPlanList();
                    alert('计划审核通过！');
                }
            }
        }
        
        function rejectPlan(id) {
            const reason = prompt('请输入拒绝原因：');
            if (reason) {
                const plan = plans.find(p => p.id === id);
                if (plan) {
                    plan.status = 'DRAFT';
                    renderPlanList();
                    alert('计划已拒绝，已退回修改！');
                }
            }
        }
        
        function publishPlan(id) {
            if (confirm('确定要发布计划吗？发布后学员可以看到并报名参加。')) {
                const plan = plans.find(p => p.id === id);
                if (plan) {
                    plan.status = 'PUBLISHED';
                    renderPlanList();
                    alert('计划发布成功！');
                }
            }
        }
        
        function startPlan(id) {
            if (confirm('确定要开始执行计划吗？')) {
                const plan = plans.find(p => p.id === id);
                if (plan) {
                    plan.status = 'ONGOING';
                    renderPlanList();
                    alert('计划开始执行！');
                }
            }
        }
        
        function completePlan(id) {
            if (confirm('确定要完成计划吗？完成后将无法再修改。')) {
                const plan = plans.find(p => p.id === id);
                if (plan) {
                    plan.status = 'COMPLETED';
                    plan.progress = 100;
                    renderPlanList();
                    alert('计划已完成！');
                }
            }
        }
        
        function deletePlan(id) {
            if (confirm('确定要删除这个计划吗？删除后将无法恢复！')) {
                const index = plans.findIndex(p => p.id === id);
                if (index > -1) {
                    plans.splice(index, 1);
                    renderPlanList();
                    alert('计划删除成功！');
                }
            }
        }
        
        function manageTrainees(id) {
            alert(`管理学员 - 计划ID: ${id}`);
        }
        
        function viewProgress(id) {
            alert(`查看进度 - 计划ID: ${id}`);
        }
        
        function viewReport(id) {
            alert(`查看报告 - 计划ID: ${id}`);
        }
        
        function exportReport(id) {
            alert(`导出报告 - 计划ID: ${id}`);
        }
        
        function exportPlans() {
            alert('导出功能开发中...');
        }
        
        // 页面加载时渲染计划列表
        window.onload = function() {
            renderPlanList();
        }
    </script>
</body>
</html>
