<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 教育培训系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 900px;
            min-height: 500px;
            display: flex;
        }
        
        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: white;
        }
        
        .login-left h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: 300;
        }
        
        .login-left p {
            font-size: 1.1em;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        .feature-list {
            margin-top: 30px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .feature-item::before {
            content: "✓";
            margin-right: 10px;
            font-weight: bold;
        }
        
        .login-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .login-form h2 {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
            font-size: 1.8em;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .captcha-group {
            display: flex;
            gap: 10px;
        }
        
        .captcha-group input {
            flex: 1;
        }
        
        .captcha-image {
            width: 120px;
            height: 50px;
            background: #f0f0f0;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-weight: bold;
            color: #666;
        }
        
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
        }
        
        .remember-me input {
            margin-right: 8px;
        }
        
        .forgot-password {
            color: #4facfe;
            text-decoration: none;
            font-size: 14px;
        }
        
        .forgot-password:hover {
            text-decoration: underline;
        }
        
        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }
        
        .back-home {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        .back-home:hover {
            background: rgba(255,255,255,0.3);
        }
        
        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                width: 90%;
                margin: 20px;
            }
            
            .login-left {
                padding: 40px 30px;
            }
            
            .login-right {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <a href="../index.html" class="back-home">← 返回首页</a>
    
    <div class="login-container">
        <div class="login-left">
            <h1>教育培训系统</h1>
            <p>专业的在线教育培训管理平台，为您提供完整的培训解决方案</p>
            
            <div class="feature-list">
                <div class="feature-item">课程资源管理</div>
                <div class="feature-item">在线学习跟踪</div>
                <div class="feature-item">智能考试系统</div>
                <div class="feature-item">培训计划管理</div>
                <div class="feature-item">数据统计分析</div>
            </div>
        </div>
        
        <div class="login-right">
            <form class="login-form">
                <h2>用户登录</h2>
                
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" placeholder="请输入用户名" required>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" placeholder="请输入密码" required>
                </div>
                
                <div class="form-group">
                    <label for="captcha">验证码</label>
                    <div class="captcha-group">
                        <input type="text" id="captcha" name="captcha" placeholder="请输入验证码" required>
                        <div class="captcha-image" onclick="refreshCaptcha()">ABCD</div>
                    </div>
                </div>
                
                <div class="form-options">
                    <label class="remember-me">
                        <input type="checkbox" name="remember">
                        记住我
                    </label>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>
                
                <button type="submit" class="login-btn" onclick="handleLogin(event)">登录</button>
            </form>
        </div>
    </div>
    
    <script>
        function refreshCaptcha() {
            const captchaElement = document.querySelector('.captcha-image');
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = '';
            for (let i = 0; i < 4; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            captchaElement.textContent = result;
        }
        
        function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const captcha = document.getElementById('captcha').value;
            
            if (!username || !password || !captcha) {
                alert('请填写完整的登录信息');
                return;
            }
            
            // 模拟登录验证
            if (username === 'admin' && password === '123456') {
                alert('登录成功！即将跳转到系统首页...');
                setTimeout(() => {
                    window.location.href = '../dashboard/dashboard.html';
                }, 1000);
            } else {
                alert('用户名或密码错误，请重试');
            }
        }
        
        // 页面加载时生成验证码
        window.onload = function() {
            refreshCaptcha();
        }
    </script>
</body>
</html>
