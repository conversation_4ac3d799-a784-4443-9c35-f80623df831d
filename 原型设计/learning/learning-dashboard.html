<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线学习 - 教育培训系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 32px;
            height: 32px;
            background: #3498db;
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
        }
        
        .menu-item:hover {
            background: #34495e;
        }
        
        .menu-item.active {
            background: #3498db;
        }
        
        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .breadcrumb {
            color: #666;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }
        
        .page-description {
            color: #666;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-right: 20px;
        }
        
        .stat-icon.blue { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.green { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .stat-icon.orange { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .stat-icon.purple { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        
        .stat-info h3 {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-info p {
            color: #666;
            font-size: 14px;
        }
        
        .learning-sections {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }
        
        .section-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
        }
        
        .section-action {
            color: #3498db;
            text-decoration: none;
            font-size: 14px;
        }
        
        .section-action:hover {
            text-decoration: underline;
        }
        
        .task-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .task-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.3s ease;
        }
        
        .task-item:hover {
            background: #f8f9fa;
        }
        
        .task-item:last-child {
            border-bottom: none;
        }
        
        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        
        .task-title {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }
        
        .task-course {
            font-size: 14px;
            color: #666;
        }
        
        .task-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-not-started {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-in-progress {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .task-progress {
            margin-bottom: 10px;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            transition: width 0.3s ease;
        }
        
        .task-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #666;
        }
        
        .task-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .recent-activity {
            padding: 20px;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #e3f2fd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #2196f3;
            font-size: 14px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #999;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .empty-text {
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">T</div>
                <div class="sidebar-title">培训系统</div>
            </div>
            <div class="sidebar-menu">
                <div class="menu-item" onclick="navigateTo('../dashboard/dashboard.html')">
                    <div class="menu-icon">📊</div>
                    <div class="menu-text">系统首页</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../user/user-list.html')">
                    <div class="menu-icon">👥</div>
                    <div class="menu-text">用户管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../course/course-list.html')">
                    <div class="menu-icon">📚</div>
                    <div class="menu-text">课程管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../instructor/instructor-list.html')">
                    <div class="menu-icon">👨‍🏫</div>
                    <div class="menu-text">教官管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../plan/plan-list.html')">
                    <div class="menu-icon">📋</div>
                    <div class="menu-text">培训计划</div>
                </div>
                <div class="menu-item active">
                    <div class="menu-icon">🎓</div>
                    <div class="menu-text">在线学习</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../exam/exam-list.html')">
                    <div class="menu-icon">📝</div>
                    <div class="menu-text">在线考试</div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="breadcrumb">首页 / 在线学习 / 学习中心</div>
                <div class="user-info">
                    <div class="avatar">学</div>
                    <span>张同学</span>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">学习中心</h1>
                    <p class="page-description">查看学习任务、跟踪学习进度、管理学习计划</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon blue">📚</div>
                        <div class="stat-info">
                            <h3>12</h3>
                            <p>总学习任务</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon green">✅</div>
                        <div class="stat-info">
                            <h3>8</h3>
                            <p>已完成任务</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon orange">⏱️</div>
                        <div class="stat-info">
                            <h3>45.5</h3>
                            <p>学习时长(小时)</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon purple">🏆</div>
                        <div class="stat-info">
                            <h3>85%</h3>
                            <p>平均完成率</p>
                        </div>
                    </div>
                </div>
                
                <div class="learning-sections">
                    <div class="section-card">
                        <div class="section-header">
                            <h3 class="section-title">我的学习任务</h3>
                            <a href="#" class="section-action">查看全部</a>
                        </div>
                        <div class="task-list" id="taskList">
                            <!-- 学习任务将通过JavaScript动态生成 -->
                        </div>
                    </div>
                    
                    <div class="section-card">
                        <div class="section-header">
                            <h3 class="section-title">最近活动</h3>
                            <a href="#" class="section-action">查看更多</a>
                        </div>
                        <div class="recent-activity" id="recentActivity">
                            <!-- 最近活动将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟学习任务数据
        const learningTasks = [
            {
                id: 1,
                taskName: 'Java基础语法学习',
                courseName: 'Java基础编程',
                status: 'IN_PROGRESS',
                progress: 75,
                duration: 120,
                completedDuration: 90,
                deadline: '2024-03-15'
            },
            {
                id: 2,
                taskName: 'Spring Boot框架入门',
                courseName: 'Spring Boot实战',
                status: 'NOT_STARTED',
                progress: 0,
                duration: 180,
                completedDuration: 0,
                deadline: '2024-03-20'
            },
            {
                id: 3,
                taskName: 'Vue.js组件开发',
                courseName: 'Vue.js前端开发',
                status: 'COMPLETED',
                progress: 100,
                duration: 150,
                completedDuration: 150,
                deadline: '2024-03-10'
            },
            {
                id: 4,
                taskName: 'MySQL数据库设计',
                courseName: 'MySQL数据库设计',
                status: 'IN_PROGRESS',
                progress: 45,
                duration: 90,
                completedDuration: 40,
                deadline: '2024-03-25'
            }
        ];
        
        // 模拟最近活动数据
        const recentActivities = [
            {
                type: 'complete',
                title: '完成了《Vue.js组件开发》学习',
                time: '2小时前'
            },
            {
                type: 'start',
                title: '开始学习《Java基础语法》',
                time: '5小时前'
            },
            {
                type: 'note',
                title: '添加了学习笔记',
                time: '1天前'
            },
            {
                type: 'achievement',
                title: '获得了"勤奋学习者"徽章',
                time: '2天前'
            }
        ];
        
        function getStatusText(status) {
            const statusMap = {
                'NOT_STARTED': '未开始',
                'IN_PROGRESS': '学习中',
                'COMPLETED': '已完成'
            };
            return statusMap[status] || status;
        }
        
        function getStatusClass(status) {
            const classMap = {
                'NOT_STARTED': 'status-not-started',
                'IN_PROGRESS': 'status-in-progress',
                'COMPLETED': 'status-completed'
            };
            return classMap[status] || '';
        }
        
        function renderTaskList() {
            const container = document.getElementById('taskList');
            container.innerHTML = '';
            
            if (learningTasks.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📚</div>
                        <div class="empty-text">暂无学习任务</div>
                    </div>
                `;
                return;
            }
            
            learningTasks.forEach(task => {
                const taskItem = document.createElement('div');
                taskItem.className = 'task-item';
                
                const actions = task.status === 'NOT_STARTED' 
                    ? `<button class="btn btn-primary" onclick="startTask(${task.id})">开始学习</button>`
                    : task.status === 'IN_PROGRESS'
                    ? `<button class="btn btn-success" onclick="continueTask(${task.id})">继续学习</button>`
                    : `<button class="btn btn-primary" onclick="reviewTask(${task.id})">复习</button>`;
                
                taskItem.innerHTML = `
                    <div class="task-header">
                        <div>
                            <div class="task-title">${task.taskName}</div>
                            <div class="task-course">${task.courseName}</div>
                        </div>
                        <div class="task-status ${getStatusClass(task.status)}">${getStatusText(task.status)}</div>
                    </div>
                    
                    <div class="task-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${task.progress}%"></div>
                        </div>
                    </div>
                    
                    <div class="task-meta">
                        <span>进度: ${task.progress}% (${task.completedDuration}/${task.duration}分钟)</span>
                        <span>截止: ${task.deadline}</span>
                    </div>
                    
                    <div class="task-actions">
                        ${actions}
                    </div>
                `;
                
                container.appendChild(taskItem);
            });
        }
        
        function renderRecentActivity() {
            const container = document.getElementById('recentActivity');
            container.innerHTML = '';
            
            recentActivities.forEach(activity => {
                const activityItem = document.createElement('div');
                activityItem.className = 'activity-item';
                
                const iconMap = {
                    'complete': '✅',
                    'start': '▶️',
                    'note': '📝',
                    'achievement': '🏆'
                };
                
                activityItem.innerHTML = `
                    <div class="activity-icon">${iconMap[activity.type] || '📌'}</div>
                    <div class="activity-content">
                        <div class="activity-title">${activity.title}</div>
                        <div class="activity-time">${activity.time}</div>
                    </div>
                `;
                
                container.appendChild(activityItem);
            });
        }
        
        function navigateTo(url) {
            window.location.href = url;
        }
        
        function startTask(taskId) {
            const task = learningTasks.find(t => t.id === taskId);
            if (task) {
                task.status = 'IN_PROGRESS';
                task.progress = 5;
                task.completedDuration = 5;
                renderTaskList();
                alert(`开始学习《${task.taskName}》`);
            }
        }
        
        function continueTask(taskId) {
            const task = learningTasks.find(t => t.id === taskId);
            if (task) {
                alert(`继续学习《${task.taskName}》，当前进度：${task.progress}%`);
                // 这里可以跳转到具体的学习页面
            }
        }
        
        function reviewTask(taskId) {
            const task = learningTasks.find(t => t.id === taskId);
            if (task) {
                alert(`复习《${task.taskName}》`);
            }
        }
        
        // 模拟学习进度更新
        function updateProgress() {
            learningTasks.forEach(task => {
                if (task.status === 'IN_PROGRESS' && task.progress < 100) {
                    task.progress = Math.min(100, task.progress + Math.random() * 2);
                    task.completedDuration = Math.floor(task.duration * task.progress / 100);
                    
                    if (task.progress >= 100) {
                        task.status = 'COMPLETED';
                        task.progress = 100;
                        task.completedDuration = task.duration;
                    }
                }
            });
            renderTaskList();
        }
        
        // 页面加载时渲染内容
        window.onload = function() {
            renderTaskList();
            renderRecentActivity();
            
            // 每30秒模拟更新一次进度
            setInterval(updateProgress, 30000);
        }
    </script>
</body>
</html>
