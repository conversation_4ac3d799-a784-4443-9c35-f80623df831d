<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 教育培训系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
        }

        .layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
        }

        .logo {
            width: 32px;
            height: 32px;
            background: #3498db;
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 500;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
        }

        .menu-item:hover {
            background: #34495e;
        }

        .menu-item.active {
            background: #3498db;
        }

        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .breadcrumb {
            color: #666;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }

        .page-description {
            color: #666;
        }

        .config-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-item {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            background: #f8f9fa;
            border-right: 1px solid #eee;
            transition: all 0.3s ease;
        }

        .tab-item:last-child {
            border-right: none;
        }

        .tab-item.active {
            background: #3498db;
            color: white;
        }

        .tab-item:hover:not(.active) {
            background: #e9ecef;
        }

        .config-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 30px;
        }

        .config-section {
            display: none;
        }

        .config-section.active {
            display: block;
        }

        .section-title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }

        .form-textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: flex;
            gap: 20px;
        }

        .form-col {
            flex: 1;
        }

        .form-help {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #3498db;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-default {
            background: #ecf0f1;
            color: #333;
            border: 1px solid #bdc3c7;
        }

        .btn-default:hover {
            background: #d5dbdb;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .config-item:last-child {
            border-bottom: none;
        }

        .config-info {
            flex: 1;
        }

        .config-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .config-desc {
            font-size: 14px;
            color: #666;
        }

        .log-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .log-table th,
        .log-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .log-table th {
            background: #f8f9fa;
            font-weight: 500;
            color: #333;
        }

        .log-level {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .level-info {
            background: #e3f2fd;
            color: #1565c0;
        }

        .level-warning {
            background: #fff3e0;
            color: #ef6c00;
        }

        .level-error {
            background: #ffebee;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">T</div>
                <div class="sidebar-title">培训系统</div>
            </div>
            <div class="sidebar-menu">
                <div class="menu-item" onclick="navigateTo('../dashboard/dashboard.html')">
                    <div class="menu-icon">📊</div>
                    <div class="menu-text">系统首页</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../user/user-list.html')">
                    <div class="menu-icon">👥</div>
                    <div class="menu-text">用户管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../course/course-list.html')">
                    <div class="menu-icon">📚</div>
                    <div class="menu-text">课程管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../instructor/instructor-list.html')">
                    <div class="menu-icon">👨‍🏫</div>
                    <div class="menu-text">教官管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../material/material-list.html')">
                    <div class="menu-icon">📄</div>
                    <div class="menu-text">教案管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../question/question-list.html')">
                    <div class="menu-icon">❓</div>
                    <div class="menu-text">考题管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../plan/plan-list.html')">
                    <div class="menu-icon">📋</div>
                    <div class="menu-text">培训计划</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../learning/learning-dashboard.html')">
                    <div class="menu-icon">🎓</div>
                    <div class="menu-text">在线学习</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../exam/exam-list.html')">
                    <div class="menu-icon">📝</div>
                    <div class="menu-text">在线考试</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../file/file-list.html')">
                    <div class="menu-icon">📁</div>
                    <div class="menu-text">文件管理</div>
                </div>
                <div class="menu-item active">
                    <div class="menu-icon">⚙️</div>
                    <div class="menu-text">系统设置</div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="header">
                <div class="breadcrumb">首页 / 系统设置 / 系统配置</div>
                <div class="user-info">
                    <div class="avatar">管</div>
                    <span>管理员</span>
                </div>
            </div>

            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">系统设置</h1>
                    <p class="page-description">管理系统配置参数、安全设置、日志管理和系统维护功能</p>
                </div>

                <div class="config-tabs">
                    <div class="tab-item active" onclick="switchTab('basic')">基础配置</div>
                    <div class="tab-item" onclick="switchTab('security')">安全设置</div>
                    <div class="tab-item" onclick="switchTab('email')">邮件配置</div>
                    <div class="tab-item" onclick="switchTab('storage')">存储配置</div>
                    <div class="tab-item" onclick="switchTab('logs')">日志管理</div>
                </div>

                <div class="config-content">
                    <!-- 基础配置 -->
                    <div class="config-section active" id="basic">
                        <h3 class="section-title">基础配置</h3>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">系统名称</label>
                                    <input type="text" class="form-input" value="教育培训系统">
                                    <div class="form-help">显示在页面标题和登录页面的系统名称</div>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">系统版本</label>
                                    <input type="text" class="form-input" value="v1.0.0" readonly>
                                    <div class="form-help">当前系统版本号</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">系统描述</label>
                            <textarea class="form-textarea" placeholder="请输入系统描述">基于Vue.js 3 + Spring Boot的现代化教育培训管理系统</textarea>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">默认语言</label>
                                    <select class="form-select">
                                        <option value="zh-CN">简体中文</option>
                                        <option value="en-US">English</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">时区设置</label>
                                    <select class="form-select">
                                        <option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</option>
                                        <option value="UTC">UTC (UTC+0)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 安全设置 -->
                    <div class="config-section" id="security">
                        <h3 class="section-title">安全设置</h3>
                        <div class="config-item">
                            <div class="config-info">
                                <div class="config-name">启用登录验证码</div>
                                <div class="config-desc">登录时需要输入图形验证码</div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="config-item">
                            <div class="config-info">
                                <div class="config-name">启用双因子认证</div>
                                <div class="config-desc">使用手机短信或邮箱进行二次验证</div>
                            </div>
                            <label class="switch">
                                <input type="checkbox">
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">密码最小长度</label>
                                    <input type="number" class="form-input" value="8" min="6" max="20">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">登录失败锁定次数</label>
                                    <input type="number" class="form-input" value="5" min="3" max="10">
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">会话超时时间(分钟)</label>
                                    <input type="number" class="form-input" value="30" min="10" max="480">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">密码过期天数</label>
                                    <input type="number" class="form-input" value="90" min="30" max="365">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 邮件配置 -->
                    <div class="config-section" id="email">
                        <h3 class="section-title">邮件配置</h3>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">SMTP服务器</label>
                                    <input type="text" class="form-input" placeholder="smtp.example.com">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">端口</label>
                                    <input type="number" class="form-input" value="587">
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">发件人邮箱</label>
                                    <input type="email" class="form-input" placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">发件人名称</label>
                                    <input type="text" class="form-input" value="教育培训系统">
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">用户名</label>
                                    <input type="text" class="form-input" placeholder="邮箱用户名">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">密码</label>
                                    <input type="password" class="form-input" placeholder="邮箱密码或授权码">
                                </div>
                            </div>
                        </div>
                        <div class="config-item">
                            <div class="config-info">
                                <div class="config-name">启用SSL加密</div>
                                <div class="config-desc">使用SSL/TLS加密连接</div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>

                    <!-- 存储配置 -->
                    <div class="config-section" id="storage">
                        <h3 class="section-title">存储配置</h3>
                        <div class="form-group">
                            <label class="form-label">存储类型</label>
                            <select class="form-select" onchange="handleStorageTypeChange(this.value)">
                                <option value="local">本地存储</option>
                                <option value="minio">MinIO对象存储</option>
                                <option value="aliyun">阿里云OSS</option>
                                <option value="qcloud">腾讯云COS</option>
                            </select>
                        </div>
                        <div id="localStorageConfig">
                            <div class="form-group">
                                <label class="form-label">本地存储路径</label>
                                <input type="text" class="form-input" value="/data/uploads">
                                <div class="form-help">文件存储的本地目录路径</div>
                            </div>
                        </div>
                        <div id="minioConfig" style="display: none;">
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">MinIO服务地址</label>
                                        <input type="text" class="form-input" placeholder="http://localhost:9000">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">存储桶名称</label>
                                        <input type="text" class="form-input" placeholder="training-system">
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Access Key</label>
                                        <input type="text" class="form-input" placeholder="访问密钥">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label class="form-label">Secret Key</label>
                                        <input type="password" class="form-input" placeholder="私有密钥">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">单文件大小限制(MB)</label>
                                    <input type="number" class="form-input" value="100" min="1" max="1024">
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">总存储空间限制(GB)</label>
                                    <input type="number" class="form-input" value="10" min="1" max="1000">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 日志管理 -->
                    <div class="config-section" id="logs">
                        <h3 class="section-title">日志管理</h3>
                        <div class="config-item">
                            <div class="config-info">
                                <div class="config-name">启用操作日志</div>
                                <div class="config-desc">记录用户的操作行为</div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="config-item">
                            <div class="config-info">
                                <div class="config-name">启用错误日志</div>
                                <div class="config-desc">记录系统错误信息</div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">日志级别</label>
                                    <select class="form-select">
                                        <option value="DEBUG">DEBUG</option>
                                        <option value="INFO" selected>INFO</option>
                                        <option value="WARN">WARN</option>
                                        <option value="ERROR">ERROR</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label class="form-label">日志保留天数</label>
                                    <input type="number" class="form-input" value="30" min="7" max="365">
                                </div>
                            </div>
                        </div>

                        <h4 style="margin: 30px 0 15px 0; color: #333;">最近日志</h4>
                        <table class="log-table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>级别</th>
                                    <th>用户</th>
                                    <th>操作</th>
                                    <th>详情</th>
                                </tr>
                            </thead>
                            <tbody id="logTableBody">
                                <!-- 日志数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="saveConfig()">保存配置</button>
                        <button class="btn btn-default" onclick="resetConfig()">重置</button>
                        <button class="btn btn-warning" onclick="exportConfig()">导出配置</button>
                        <button class="btn btn-success" onclick="importConfig()">导入配置</button>
                        <button class="btn btn-danger" onclick="clearCache()">清除缓存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟日志数据
        const logs = [
            {time: '2024-01-15 14:30:25', level: 'INFO', user: '管理员', action: '用户登录', detail: '管理员登录系统'},
            {time: '2024-01-15 14:25:12', level: 'INFO', user: '张教授', action: '上传教案', detail: '上传Java基础教程.pdf'},
            {time: '2024-01-15 14:20:08', level: 'WARNING', user: '李老师', action: '登录失败', detail: '密码错误，剩余尝试次数：2'},
            {time: '2024-01-15 14:15:33', level: 'INFO', user: '王工程师', action: '创建课程', detail: '创建MySQL数据库设计课程'},
            {time: '2024-01-15 14:10:45', level: 'ERROR', user: '系统', action: '文件上传', detail: '文件大小超出限制：150MB > 100MB'},
            {time: '2024-01-15 14:05:22', level: 'INFO', user: '陈老师', action: '修改题目', detail: '修改Vue.js相关题目'},
            {time: '2024-01-15 14:00:15', level: 'INFO', user: '管理员', action: '系统配置', detail: '修改邮件服务器配置'}
        ];

        function switchTab(tabName) {
            // 隐藏所有配置区域
            const sections = document.querySelectorAll('.config-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // 移除所有标签的激活状态
            const tabs = document.querySelectorAll('.tab-item');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的配置区域
            document.getElementById(tabName).classList.add('active');

            // 激活选中的标签
            event.target.classList.add('active');

            // 如果是日志管理标签，渲染日志表格
            if (tabName === 'logs') {
                renderLogTable();
            }
        }

        function renderLogTable() {
            const tbody = document.getElementById('logTableBody');
            tbody.innerHTML = '';

            logs.forEach(log => {
                const row = document.createElement('tr');
                const levelClass = `level-${log.level.toLowerCase()}`;

                row.innerHTML = `
                    <td>${log.time}</td>
                    <td><span class="log-level ${levelClass}">${log.level}</span></td>
                    <td>${log.user}</td>
                    <td>${log.action}</td>
                    <td>${log.detail}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function handleStorageTypeChange(type) {
            // 隐藏所有存储配置
            document.getElementById('localStorageConfig').style.display = 'none';
            document.getElementById('minioConfig').style.display = 'none';

            // 显示选中的存储配置
            if (type === 'local') {
                document.getElementById('localStorageConfig').style.display = 'block';
            } else if (type === 'minio') {
                document.getElementById('minioConfig').style.display = 'block';
            }
        }

        function navigateTo(url) {
            window.location.href = url;
        }

        function saveConfig() {
            alert('配置保存成功！');
        }

        function resetConfig() {
            if (confirm('确定要重置所有配置吗？这将恢复到默认设置。')) {
                document.querySelectorAll('.config-content input, .config-content select, .config-content textarea').forEach(element => {
                    if (element.type === 'checkbox') {
                        element.checked = element.hasAttribute('checked');
                    } else if (element.hasAttribute('value')) {
                        element.value = element.getAttribute('value') || '';
                    } else {
                        element.value = '';
                    }
                });
                alert('配置已重置！');
            }
        }

        function exportConfig() {
            const config = {
                basic: {
                    systemName: '教育培训系统',
                    version: 'v1.0.0',
                    description: '基于Vue.js 3 + Spring Boot的现代化教育培训管理系统'
                },
                security: {
                    enableCaptcha: true,
                    enable2FA: false,
                    minPasswordLength: 8,
                    maxLoginAttempts: 5
                }
            };

            const dataStr = JSON.stringify(config, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = 'system-config.json';
            link.click();

            URL.revokeObjectURL(url);
            alert('配置导出成功！');
        }

        function importConfig() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const config = JSON.parse(e.target.result);
                            alert('配置导入成功！');
                        } catch (error) {
                            alert('配置文件格式错误！');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        function clearCache() {
            if (confirm('确定要清除系统缓存吗？这可能会影响系统性能。')) {
                setTimeout(() => {
                    alert('缓存清除成功！');
                }, 1000);
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            // 默认显示基础配置
            const basicTab = document.querySelector('.tab-item[onclick*="basic"]');
            if (basicTab) {
                basicTab.click();
            }
        }
    </script>
</body>
</html>