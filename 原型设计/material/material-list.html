<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教案管理 - 教育培训系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
        }

        .layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
        }

        .logo {
            width: 32px;
            height: 32px;
            background: #3498db;
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 500;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
        }

        .menu-item:hover {
            background: #34495e;
        }

        .menu-item.active {
            background: #3498db;
        }

        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .breadcrumb {
            color: #666;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }

        .page-description {
            color: #666;
        }

        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .toolbar-left {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .toolbar-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-default {
            background: #ecf0f1;
            color: #333;
            border: 1px solid #bdc3c7;
        }

        .btn-default:hover {
            background: #d5dbdb;
        }

        .search-box {
            display: flex;
            gap: 10px;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        .material-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 500;
            color: #333;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-published {
            background: #d4edda;
            color: #155724;
        }

        .status-draft {
            background: #fff3cd;
            color: #856404;
        }

        .status-reviewing {
            background: #cce7ff;
            color: #004085;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .file-icon {
            font-size: 18px;
            margin-right: 8px;
        }

        .file-info {
            display: flex;
            align-items: center;
        }

        .file-name {
            font-weight: 500;
            color: #333;
        }

        .file-size {
            font-size: 12px;
            color: #666;
            margin-left: 8px;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .pagination {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            color: #333;
        }

        .page-btn:hover {
            background: #f0f0f0;
        }

        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">T</div>
                <div class="sidebar-title">培训系统</div>
            </div>
            <div class="sidebar-menu">
                <div class="menu-item" onclick="navigateTo('../dashboard/dashboard.html')">
                    <div class="menu-icon">📊</div>
                    <div class="menu-text">系统首页</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../user/user-list.html')">
                    <div class="menu-icon">👥</div>
                    <div class="menu-text">用户管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../course/course-list.html')">
                    <div class="menu-icon">📚</div>
                    <div class="menu-text">课程管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../instructor/instructor-list.html')">
                    <div class="menu-icon">👨‍🏫</div>
                    <div class="menu-text">教官管理</div>
                </div>
                <div class="menu-item active">
                    <div class="menu-icon">📄</div>
                    <div class="menu-text">教案管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../question/question-list.html')">
                    <div class="menu-icon">❓</div>
                    <div class="menu-text">考题管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../plan/plan-list.html')">
                    <div class="menu-icon">📋</div>
                    <div class="menu-text">培训计划</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../learning/learning-dashboard.html')">
                    <div class="menu-icon">🎓</div>
                    <div class="menu-text">在线学习</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../exam/exam-list.html')">
                    <div class="menu-icon">📝</div>
                    <div class="menu-text">在线考试</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../file/file-list.html')">
                    <div class="menu-icon">📁</div>
                    <div class="menu-text">文件管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../system/system-config.html')">
                    <div class="menu-icon">⚙️</div>
                    <div class="menu-text">系统设置</div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="header">
                <div class="breadcrumb">首页 / 教案管理 / 教案列表</div>
                <div class="user-info">
                    <div class="avatar">管</div>
                    <span>管理员</span>
                </div>
            </div>

            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">教案管理</h1>
                    <p class="page-description">管理培训教案文件，包括上传、版本控制、审核流程和下载统计</p>
                </div>

                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="showUploadModal()">+ 上传教案</button>
                        <button class="btn btn-success" onclick="batchApprove()">批量审核</button>
                        <button class="btn btn-default" onclick="exportMaterials()">导出列表</button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <select class="filter-select">
                                <option value="">全部课程</option>
                                <option value="1">Java基础编程</option>
                                <option value="2">Spring Boot实战</option>
                                <option value="3">MySQL数据库设计</option>
                                <option value="4">Vue.js前端开发</option>
                            </select>
                            <select class="filter-select">
                                <option value="">全部状态</option>
                                <option value="published">已发布</option>
                                <option value="draft">草稿</option>
                                <option value="reviewing">审核中</option>
                                <option value="rejected">已拒绝</option>
                            </select>
                            <input type="text" class="search-input" placeholder="搜索教案名称">
                            <button class="btn btn-primary">搜索</button>
                        </div>
                    </div>
                </div>

                <div class="material-table">
                    <table class="table" id="materialTable">
                        <thead>
                            <tr>
                                <th>教案信息</th>
                                <th>所属课程</th>
                                <th>版本</th>
                                <th>上传者</th>
                                <th>上传时间</th>
                                <th>状态</th>
                                <th>下载次数</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="materialTableBody">
                            <!-- 教案数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 上传教案模态框 -->
    <div class="modal" id="uploadModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div class="modal-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; padding: 30px; width: 600px; max-width: 90%; max-height: 80vh; overflow-y: auto;">
            <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 class="modal-title">上传教案</h3>
                <button class="close-btn" onclick="closeUploadModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">&times;</button>
            </div>
            <form id="uploadForm">
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">教案名称 *</label>
                    <input type="text" class="form-input" name="materialName" required style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                </div>
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">所属课程 *</label>
                    <select class="form-select" name="courseId" required style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; background: white;">
                        <option value="">请选择课程</option>
                        <option value="1">Java基础编程</option>
                        <option value="2">Spring Boot实战</option>
                        <option value="3">MySQL数据库设计</option>
                        <option value="4">Vue.js前端开发</option>
                    </select>
                </div>
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">教案文件 *</label>
                    <input type="file" class="form-input" name="materialFile" accept=".pdf,.doc,.docx,.ppt,.pptx" required style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    <small style="color: #666; font-size: 12px;">支持格式：PDF、Word、PowerPoint，最大50MB</small>
                </div>
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">版本说明</label>
                    <textarea class="form-textarea" name="versionNote" placeholder="请输入版本更新说明" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; resize: vertical; min-height: 60px;"></textarea>
                </div>
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">教案描述</label>
                    <textarea class="form-textarea" name="description" placeholder="请输入教案描述" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; resize: vertical; min-height: 80px;"></textarea>
                </div>
            </form>
            <div class="modal-footer" style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;">
                <button class="btn btn-default" onclick="closeUploadModal()">取消</button>
                <button class="btn btn-primary" onclick="uploadMaterial()">上传</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟教案数据
        const materials = [
            {id: 1, name: 'Java基础语法教案.pdf', course: 'Java基础编程', version: 'v2.1', uploader: '张教授', uploadTime: '2024-01-15', status: 'published', downloads: 156, fileSize: '2.3MB', fileType: 'pdf'},
            {id: 2, name: 'Spring Boot入门指南.docx', course: 'Spring Boot实战', version: 'v1.0', uploader: '李老师', uploadTime: '2024-01-12', status: 'reviewing', downloads: 0, fileSize: '1.8MB', fileType: 'docx'},
            {id: 3, name: 'MySQL优化实践.pptx', course: 'MySQL数据库设计', version: 'v1.2', uploader: '王工程师', uploadTime: '2024-01-10', status: 'draft', downloads: 23, fileSize: '4.1MB', fileType: 'pptx'},
            {id: 4, name: 'Vue组件开发教程.pdf', course: 'Vue.js前端开发', version: 'v3.0', uploader: '陈老师', uploadTime: '2024-01-08', status: 'published', downloads: 89, fileSize: '3.2MB', fileType: 'pdf'},
            {id: 5, name: 'Java面向对象编程.doc', course: 'Java基础编程', version: 'v1.5', uploader: '张教授', uploadTime: '2024-01-05', status: 'rejected', downloads: 12, fileSize: '1.5MB', fileType: 'doc'}
        ];

        function getFileIcon(fileType) {
            const icons = {
                'pdf': '📄',
                'doc': '📝',
                'docx': '📝',
                'ppt': '📊',
                'pptx': '📊'
            };
            return icons[fileType] || '📄';
        }

        function getStatusBadge(status) {
            const statusMap = {
                'published': { class: 'status-published', text: '已发布' },
                'draft': { class: 'status-draft', text: '草稿' },
                'reviewing': { class: 'status-reviewing', text: '审核中' },
                'rejected': { class: 'status-rejected', text: '已拒绝' }
            };
            const statusInfo = statusMap[status] || { class: 'status-draft', text: '未知' };
            return `<span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
        }

        function renderMaterialTable() {
            const tbody = document.getElementById('materialTableBody');
            tbody.innerHTML = '';

            materials.forEach(material => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="file-info">
                            <span class="file-icon">${getFileIcon(material.fileType)}</span>
                            <div>
                                <div class="file-name">${material.name}</div>
                                <span class="file-size">${material.fileSize}</span>
                            </div>
                        </div>
                    </td>
                    <td>${material.course}</td>
                    <td>${material.version}</td>
                    <td>${material.uploader}</td>
                    <td>${material.uploadTime}</td>
                    <td>${getStatusBadge(material.status)}</td>
                    <td>${material.downloads}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-info btn-sm" onclick="downloadMaterial(${material.id})">下载</button>
                            <button class="btn btn-warning btn-sm" onclick="editMaterial(${material.id})">编辑</button>
                            <button class="btn btn-danger btn-sm" onclick="deleteMaterial(${material.id})">删除</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function navigateTo(url) {
            window.location.href = url;
        }

        function showUploadModal() {
            document.getElementById('uploadModal').style.display = 'block';
        }

        function closeUploadModal() {
            document.getElementById('uploadModal').style.display = 'none';
            document.getElementById('uploadForm').reset();
        }

        function uploadMaterial() {
            const form = document.getElementById('uploadForm');
            const formData = new FormData(form);

            if (!formData.get('materialName') || !formData.get('courseId') || !formData.get('materialFile')) {
                alert('请填写必填字段！');
                return;
            }

            alert('教案上传成功！');
            closeUploadModal();
        }

        function downloadMaterial(id) {
            const material = materials.find(m => m.id === id);
            if (material) {
                alert(`下载教案：${material.name}`);
            }
        }

        function editMaterial(id) {
            alert(`编辑教案 - ID: ${id}`);
        }

        function deleteMaterial(id) {
            if (confirm('确定要删除这个教案吗？删除后将无法恢复！')) {
                const index = materials.findIndex(m => m.id === id);
                if (index > -1) {
                    materials.splice(index, 1);
                    renderMaterialTable();
                    alert('教案删除成功！');
                }
            }
        }

        function batchApprove() {
            alert('批量审核功能开发中...');
        }

        function exportMaterials() {
            alert('导出功能开发中...');
        }

        // 页面加载时渲染表格
        window.onload = function() {
            renderMaterialTable();
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('uploadModal');
            if (event.target === modal) {
                closeUploadModal();
            }
        }
    </script>