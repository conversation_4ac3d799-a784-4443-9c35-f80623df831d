<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线考试 - 教育培训系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 32px;
            height: 32px;
            background: #3498db;
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
        }
        
        .menu-item:hover {
            background: #34495e;
        }
        
        .menu-item.active {
            background: #3498db;
        }
        
        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .breadcrumb {
            color: #666;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }
        
        .page-description {
            color: #666;
        }
        
        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .toolbar-left {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .toolbar-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-default {
            background: #ecf0f1;
            color: #333;
            border: 1px solid #bdc3c7;
        }
        
        .btn-default:hover {
            background: #d5dbdb;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
        }
        
        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .exam-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .exam-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .exam-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .exam-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            position: relative;
        }
        
        .exam-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-draft {
            background: rgba(241, 196, 15, 0.9);
            color: white;
        }
        
        .status-published {
            background: rgba(39, 174, 96, 0.9);
            color: white;
        }
        
        .status-ongoing {
            background: rgba(52, 152, 219, 0.9);
            color: white;
        }
        
        .status-completed {
            background: rgba(149, 165, 166, 0.9);
            color: white;
        }
        
        .exam-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .exam-code {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .exam-content {
            padding: 20px;
        }
        
        .exam-meta {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }
        
        .meta-icon {
            width: 16px;
            text-align: center;
        }
        
        .exam-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .exam-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 15px;
            padding: 15px 0;
            border-top: 1px solid #f0f0f0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 16px;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        
        .exam-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-warning:hover {
            background: #e67e22;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .countdown {
            background: #fff3cd;
            color: #856404;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .countdown.urgent {
            background: #f8d7da;
            color: #721c24;
        }
        
        .pagination {
            margin-top: 30px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            color: #333;
        }
        
        .page-btn:hover {
            background: #f0f0f0;
        }
        
        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .empty-text {
            font-size: 16px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">T</div>
                <div class="sidebar-title">培训系统</div>
            </div>
            <div class="sidebar-menu">
                <div class="menu-item" onclick="navigateTo('../dashboard/dashboard.html')">
                    <div class="menu-icon">📊</div>
                    <div class="menu-text">系统首页</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../user/user-list.html')">
                    <div class="menu-icon">👥</div>
                    <div class="menu-text">用户管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../course/course-list.html')">
                    <div class="menu-icon">📚</div>
                    <div class="menu-text">课程管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../instructor/instructor-list.html')">
                    <div class="menu-icon">👨‍🏫</div>
                    <div class="menu-text">教官管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../plan/plan-list.html')">
                    <div class="menu-icon">📋</div>
                    <div class="menu-text">培训计划</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../learning/learning-dashboard.html')">
                    <div class="menu-icon">🎓</div>
                    <div class="menu-text">在线学习</div>
                </div>
                <div class="menu-item active">
                    <div class="menu-icon">📝</div>
                    <div class="menu-text">在线考试</div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="breadcrumb">首页 / 在线考试 / 考试列表</div>
                <div class="user-info">
                    <div class="avatar">管</div>
                    <span>管理员</span>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">在线考试</h1>
                    <p class="page-description">管理在线考试的创建、发布、监考和成绩统计</p>
                </div>
                
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="createExam()">+ 创建考试</button>
                        <button class="btn btn-default" onclick="exportResults()">导出成绩</button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <select class="filter-select">
                                <option value="">全部状态</option>
                                <option value="DRAFT">草稿</option>
                                <option value="PUBLISHED">已发布</option>
                                <option value="ONGOING">进行中</option>
                                <option value="COMPLETED">已完成</option>
                            </select>
                            <select class="filter-select">
                                <option value="">全部类型</option>
                                <option value="final">期末考试</option>
                                <option value="midterm">期中考试</option>
                                <option value="quiz">随堂测验</option>
                                <option value="practice">练习测试</option>
                            </select>
                            <input type="text" class="search-input" placeholder="搜索考试名称">
                            <button class="btn btn-primary">搜索</button>
                        </div>
                    </div>
                </div>
                
                <div class="exam-grid" id="examGrid">
                    <!-- 考试卡片将通过JavaScript动态生成 -->
                </div>
                
                <div class="pagination">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟考试数据
        const exams = [
            {
                id: 1,
                examName: 'Java基础知识测试',
                examCode: 'EXAM_JAVA_BASIC_001',
                status: 'ONGOING',
                examType: 'final',
                startTime: '2024-03-15 09:00',
                endTime: '2024-03-15 11:00',
                duration: 120,
                totalScore: 100,
                passScore: 60,
                questionCount: 50,
                participantCount: 25,
                submittedCount: 18,
                description: 'Java编程语言基础知识综合测试，包含语法、面向对象、集合框架等内容'
            },
            {
                id: 2,
                examName: 'Spring Boot框架测试',
                examCode: 'EXAM_SPRING_001',
                status: 'PUBLISHED',
                examType: 'midterm',
                startTime: '2024-03-20 14:00',
                endTime: '2024-03-20 16:00',
                duration: 120,
                totalScore: 100,
                passScore: 70,
                questionCount: 40,
                participantCount: 30,
                submittedCount: 0,
                description: 'Spring Boot框架应用开发测试，涵盖自动配置、Web开发、数据访问等'
            },
            {
                id: 3,
                examName: 'Vue.js前端开发测试',
                examCode: 'EXAM_VUE_001',
                status: 'COMPLETED',
                examType: 'final',
                startTime: '2024-03-10 10:00',
                endTime: '2024-03-10 12:00',
                duration: 120,
                totalScore: 100,
                passScore: 60,
                questionCount: 45,
                participantCount: 22,
                submittedCount: 22,
                description: 'Vue.js前端框架开发技能测试，包含组件开发、路由管理、状态管理等'
            },
            {
                id: 4,
                examName: '数据库设计与优化',
                examCode: 'EXAM_DB_001',
                status: 'DRAFT',
                examType: 'quiz',
                startTime: '2024-03-25 15:00',
                endTime: '2024-03-25 16:30',
                duration: 90,
                totalScore: 80,
                passScore: 48,
                questionCount: 30,
                participantCount: 0,
                submittedCount: 0,
                description: '数据库设计原理和性能优化技术测试'
            }
        ];
        
        function getStatusText(status) {
            const statusMap = {
                'DRAFT': '草稿',
                'PUBLISHED': '已发布',
                'ONGOING': '进行中',
                'COMPLETED': '已完成'
            };
            return statusMap[status] || status;
        }
        
        function getTypeText(type) {
            const typeMap = {
                'final': '期末考试',
                'midterm': '期中考试',
                'quiz': '随堂测验',
                'practice': '练习测试'
            };
            return typeMap[type] || type;
        }
        
        function getTimeRemaining(startTime) {
            const now = new Date();
            const start = new Date(startTime);
            const diff = start - now;
            
            if (diff <= 0) return null;
            
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            
            if (hours > 24) {
                const days = Math.floor(hours / 24);
                return `${days}天后开始`;
            } else if (hours > 0) {
                return `${hours}小时${minutes}分钟后开始`;
            } else {
                return `${minutes}分钟后开始`;
            }
        }
        
        function renderExamGrid() {
            const grid = document.getElementById('examGrid');
            grid.innerHTML = '';
            
            if (exams.length === 0) {
                grid.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📝</div>
                        <div class="empty-text">暂无考试安排</div>
                        <button class="btn btn-primary" onclick="createExam()">创建第一个考试</button>
                    </div>
                `;
                return;
            }
            
            exams.forEach(exam => {
                const card = document.createElement('div');
                card.className = 'exam-card';
                
                const timeRemaining = getTimeRemaining(exam.startTime);
                const countdownHtml = timeRemaining ? 
                    `<div class="countdown ${timeRemaining.includes('分钟') ? 'urgent' : ''}">${timeRemaining}</div>` : '';
                
                const actions = getActionsForStatus(exam.status, exam.id);
                
                card.innerHTML = `
                    <div class="exam-header">
                        <div class="exam-status status-${exam.status.toLowerCase()}">${getStatusText(exam.status)}</div>
                        <div class="exam-title">${exam.examName}</div>
                        <div class="exam-code">${exam.examCode}</div>
                    </div>
                    <div class="exam-content">
                        ${countdownHtml}
                        <div class="exam-meta">
                            <div class="meta-item">
                                <span class="meta-icon">📅</span>
                                <span>${exam.startTime}</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-icon">⏱️</span>
                                <span>${exam.duration}分钟</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-icon">📋</span>
                                <span>${getTypeText(exam.examType)}</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-icon">🎯</span>
                                <span>${exam.passScore}分及格</span>
                            </div>
                        </div>
                        
                        <div class="exam-description">${exam.description}</div>
                        
                        <div class="exam-stats">
                            <div class="stat-item">
                                <div class="stat-number">${exam.questionCount}</div>
                                <div class="stat-label">题目数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${exam.participantCount}</div>
                                <div class="stat-label">参考人数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${exam.submittedCount}</div>
                                <div class="stat-label">已提交</div>
                            </div>
                        </div>
                        
                        <div class="exam-actions">
                            ${actions}
                        </div>
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }
        
        function getActionsForStatus(status, examId) {
            const baseActions = `<button class="btn btn-info btn-sm" onclick="viewExam(${examId})">查看详情</button>`;
            
            switch (status) {
                case 'DRAFT':
                    return baseActions + `
                        <button class="btn btn-warning btn-sm" onclick="editExam(${examId})">编辑</button>
                        <button class="btn btn-success btn-sm" onclick="publishExam(${examId})">发布</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteExam(${examId})">删除</button>
                    `;
                case 'PUBLISHED':
                    return baseActions + `
                        <button class="btn btn-success btn-sm" onclick="startExam(${examId})">开始考试</button>
                        <button class="btn btn-warning btn-sm" onclick="editExam(${examId})">编辑</button>
                    `;
                case 'ONGOING':
                    return baseActions + `
                        <button class="btn btn-info btn-sm" onclick="monitorExam(${examId})">监考</button>
                        <button class="btn btn-success btn-sm" onclick="endExam(${examId})">结束考试</button>
                    `;
                case 'COMPLETED':
                    return baseActions + `
                        <button class="btn btn-info btn-sm" onclick="viewResults(${examId})">查看成绩</button>
                        <button class="btn btn-default btn-sm" onclick="exportResults(${examId})">导出成绩</button>
                    `;
                default:
                    return baseActions;
            }
        }
        
        function navigateTo(url) {
            window.location.href = url;
        }
        
        function createExam() {
            alert('创建考试功能开发中...');
        }
        
        function viewExam(id) {
            alert(`查看考试详情 - ID: ${id}`);
        }
        
        function editExam(id) {
            alert(`编辑考试 - ID: ${id}`);
        }
        
        function publishExam(id) {
            if (confirm('确定要发布考试吗？发布后考生可以看到考试安排。')) {
                const exam = exams.find(e => e.id === id);
                if (exam) {
                    exam.status = 'PUBLISHED';
                    renderExamGrid();
                    alert('考试发布成功！');
                }
            }
        }
        
        function startExam(id) {
            if (confirm('确定要开始考试吗？开始后考生可以进入考试。')) {
                const exam = exams.find(e => e.id === id);
                if (exam) {
                    exam.status = 'ONGOING';
                    renderExamGrid();
                    alert('考试已开始！');
                }
            }
        }
        
        function endExam(id) {
            if (confirm('确定要结束考试吗？结束后将自动提交所有未提交的试卷。')) {
                const exam = exams.find(e => e.id === id);
                if (exam) {
                    exam.status = 'COMPLETED';
                    exam.submittedCount = exam.participantCount;
                    renderExamGrid();
                    alert('考试已结束！');
                }
            }
        }
        
        function deleteExam(id) {
            if (confirm('确定要删除这个考试吗？删除后将无法恢复！')) {
                const index = exams.findIndex(e => e.id === id);
                if (index > -1) {
                    exams.splice(index, 1);
                    renderExamGrid();
                    alert('考试删除成功！');
                }
            }
        }
        
        function monitorExam(id) {
            alert(`进入监考模式 - 考试ID: ${id}`);
        }
        
        function viewResults(id) {
            alert(`查看考试成绩 - 考试ID: ${id}`);
        }
        
        function exportResults(id) {
            if (id) {
                alert(`导出考试成绩 - 考试ID: ${id}`);
            } else {
                alert('导出所有考试成绩');
            }
        }
        
        // 页面加载时渲染考试网格
        window.onload = function() {
            renderExamGrid();
            
            // 每分钟更新一次倒计时
            setInterval(renderExamGrid, 60000);
        }
    </script>
</body>
</html>
