<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 教育培训系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 32px;
            height: 32px;
            background: #3498db;
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
        }
        
        .menu-item:hover {
            background: #34495e;
        }
        
        .menu-item.active {
            background: #3498db;
        }
        
        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .breadcrumb {
            color: #666;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }
        
        .page-description {
            color: #666;
        }
        
        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .toolbar-left {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .toolbar-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-default {
            background: #ecf0f1;
            color: #333;
            border: 1px solid #bdc3c7;
        }
        
        .btn-default:hover {
            background: #d5dbdb;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
        }
        
        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 500;
            color: #333;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .pagination {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .pagination-info {
            color: #666;
            font-size: 14px;
        }
        
        .pagination-controls {
            display: flex;
            gap: 5px;
        }
        
        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .page-btn:hover {
            background: #f0f0f0;
        }
        
        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 30px;
            width: 500px;
            max-width: 90%;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">T</div>
                <div class="sidebar-title">培训系统</div>
            </div>
            <div class="sidebar-menu">
                <div class="menu-item" onclick="navigateTo('../dashboard/dashboard.html')">
                    <div class="menu-icon">📊</div>
                    <div class="menu-text">系统首页</div>
                </div>
                <div class="menu-item active">
                    <div class="menu-icon">👥</div>
                    <div class="menu-text">用户管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../course/course-list.html')">
                    <div class="menu-icon">📚</div>
                    <div class="menu-text">课程管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../instructor/instructor-list.html')">
                    <div class="menu-icon">👨‍🏫</div>
                    <div class="menu-text">教官管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../plan/plan-list.html')">
                    <div class="menu-icon">📋</div>
                    <div class="menu-text">培训计划</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../learning/learning-dashboard.html')">
                    <div class="menu-icon">🎓</div>
                    <div class="menu-text">在线学习</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../exam/exam-list.html')">
                    <div class="menu-icon">📝</div>
                    <div class="menu-text">在线考试</div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="breadcrumb">首页 / 用户管理 / 用户列表</div>
                <div class="user-info">
                    <div class="avatar">管</div>
                    <span>管理员</span>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">用户管理</h1>
                    <p class="page-description">管理系统用户信息，包括用户创建、编辑、删除和权限分配</p>
                </div>
                
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="showAddModal()">+ 新增用户</button>
                        <button class="btn btn-danger" onclick="batchDelete()">批量删除</button>
                        <button class="btn btn-default" onclick="exportUsers()">导出数据</button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <select class="filter-select">
                                <option value="">全部部门</option>
                                <option value="1">技术部</option>
                                <option value="2">人事部</option>
                                <option value="3">财务部</option>
                            </select>
                            <select class="filter-select">
                                <option value="">全部状态</option>
                                <option value="1">正常</option>
                                <option value="0">禁用</option>
                            </select>
                            <input type="text" class="search-input" placeholder="搜索用户名或姓名">
                            <button class="btn btn-primary">搜索</button>
                        </div>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" onchange="selectAll(this)"></th>
                                <th>用户ID</th>
                                <th>用户名</th>
                                <th>真实姓名</th>
                                <th>邮箱</th>
                                <th>手机号</th>
                                <th>部门</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody">
                            <!-- 用户数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                    
                    <div class="pagination">
                        <div class="pagination-info">
                            显示第 1-10 条，共 156 条记录
                        </div>
                        <div class="pagination-controls">
                            <button class="page-btn">上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">4</button>
                            <button class="page-btn">5</button>
                            <button class="page-btn">下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 新增/编辑用户模态框 -->
    <div class="modal" id="userModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">新增用户</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <form id="userForm">
                <div class="form-group">
                    <label class="form-label">用户名 *</label>
                    <input type="text" class="form-input" name="username" required>
                </div>
                <div class="form-group">
                    <label class="form-label">密码 *</label>
                    <input type="password" class="form-input" name="password" required>
                </div>
                <div class="form-group">
                    <label class="form-label">真实姓名 *</label>
                    <input type="text" class="form-input" name="realName" required>
                </div>
                <div class="form-group">
                    <label class="form-label">邮箱</label>
                    <input type="email" class="form-input" name="email">
                </div>
                <div class="form-group">
                    <label class="form-label">手机号</label>
                    <input type="tel" class="form-input" name="phone">
                </div>
                <div class="form-group">
                    <label class="form-label">部门</label>
                    <select class="form-select" name="departmentId">
                        <option value="">请选择部门</option>
                        <option value="1">技术部</option>
                        <option value="2">人事部</option>
                        <option value="3">财务部</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">角色</label>
                    <select class="form-select" name="roleId">
                        <option value="">请选择角色</option>
                        <option value="1">管理员</option>
                        <option value="2">教官</option>
                        <option value="3">学员</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">状态</label>
                    <select class="form-select" name="status">
                        <option value="1">正常</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
            </form>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" onclick="saveUser()">保存</button>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟用户数据
        const users = [
            {id: 1, username: 'admin', realName: '系统管理员', email: '<EMAIL>', phone: '13800138000', department: '技术部', role: '管理员', status: 1, createTime: '2024-01-01'},
            {id: 2, username: 'teacher1', realName: '张老师', email: '<EMAIL>', phone: '13800138001', department: '技术部', role: '教官', status: 1, createTime: '2024-01-02'},
            {id: 3, username: 'student1', realName: '李同学', email: '<EMAIL>', phone: '13800138002', department: '技术部', role: '学员', status: 1, createTime: '2024-01-03'},
            {id: 4, username: 'teacher2', realName: '王老师', email: '<EMAIL>', phone: '13800138003', department: '人事部', role: '教官', status: 0, createTime: '2024-01-04'},
            {id: 5, username: 'student2', realName: '赵同学', email: '<EMAIL>', phone: '13800138004', department: '财务部', role: '学员', status: 1, createTime: '2024-01-05'}
        ];
        
        function renderUserTable() {
            const tbody = document.getElementById('userTableBody');
            tbody.innerHTML = '';
            
            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" value="${user.id}"></td>
                    <td>${user.id}</td>
                    <td>${user.username}</td>
                    <td>${user.realName}</td>
                    <td>${user.email}</td>
                    <td>${user.phone}</td>
                    <td>${user.department}</td>
                    <td>${user.role}</td>
                    <td><span class="status-badge ${user.status ? 'status-active' : 'status-inactive'}">${user.status ? '正常' : '禁用'}</span></td>
                    <td>${user.createTime}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-primary btn-sm" onclick="editUser(${user.id})">编辑</button>
                            <button class="btn btn-danger btn-sm" onclick="deleteUser(${user.id})">删除</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        
        function navigateTo(url) {
            window.location.href = url;
        }
        
        function showAddModal() {
            document.getElementById('modalTitle').textContent = '新增用户';
            document.getElementById('userForm').reset();
            document.getElementById('userModal').style.display = 'block';
        }
        
        function editUser(id) {
            const user = users.find(u => u.id === id);
            if (user) {
                document.getElementById('modalTitle').textContent = '编辑用户';
                // 填充表单数据
                const form = document.getElementById('userForm');
                form.username.value = user.username;
                form.realName.value = user.realName;
                form.email.value = user.email;
                form.phone.value = user.phone;
                form.status.value = user.status;
                document.getElementById('userModal').style.display = 'block';
            }
        }
        
        function deleteUser(id) {
            if (confirm('确定要删除这个用户吗？')) {
                const index = users.findIndex(u => u.id === id);
                if (index > -1) {
                    users.splice(index, 1);
                    renderUserTable();
                    alert('用户删除成功！');
                }
            }
        }
        
        function closeModal() {
            document.getElementById('userModal').style.display = 'none';
        }
        
        function saveUser() {
            const form = document.getElementById('userForm');
            const formData = new FormData(form);
            
            // 简单验证
            if (!formData.get('username') || !formData.get('realName')) {
                alert('请填写必填字段！');
                return;
            }
            
            alert('用户保存成功！');
            closeModal();
        }
        
        function selectAll(checkbox) {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = checkbox.checked);
        }
        
        function batchDelete() {
            const selected = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            if (selected.length === 0) {
                alert('请选择要删除的用户！');
                return;
            }
            
            if (confirm(`确定要删除选中的 ${selected.length} 个用户吗？`)) {
                alert('批量删除成功！');
                // 这里应该调用删除API
            }
        }
        
        function exportUsers() {
            alert('导出功能开发中...');
        }
        
        // 页面加载时渲染表格
        window.onload = function() {
            renderUserTable();
        }
    </script>
</body>
</html>
