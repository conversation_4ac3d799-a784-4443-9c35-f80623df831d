<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件管理 - 教育培训系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
        }

        .layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
        }

        .logo {
            width: 32px;
            height: 32px;
            background: #3498db;
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 500;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
        }

        .menu-item:hover {
            background: #34495e;
        }

        .menu-item.active {
            background: #3498db;
        }

        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .breadcrumb {
            color: #666;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }

        .page-description {
            color: #666;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .toolbar-left {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .toolbar-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-default {
            background: #ecf0f1;
            color: #333;
            border: 1px solid #bdc3c7;
        }

        .btn-default:hover {
            background: #d5dbdb;
        }

        .search-box {
            display: flex;
            gap: 10px;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        .file-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 500;
            color: #333;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .file-info {
            display: flex;
            align-items: center;
        }

        .file-icon {
            font-size: 24px;
            margin-right: 12px;
        }

        .file-details {
            flex: 1;
        }

        .file-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }

        .file-path {
            font-size: 12px;
            color: #666;
        }

        .file-size {
            color: #666;
            font-size: 14px;
        }

        .file-type-badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .type-image {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .type-document {
            background: #e3f2fd;
            color: #1565c0;
        }

        .type-video {
            background: #fff3e0;
            color: #ef6c00;
        }

        .type-archive {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .type-other {
            background: #f5f5f5;
            color: #616161;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .pagination {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            color: #333;
        }

        .page-btn:hover {
            background: #f0f0f0;
        }

        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">T</div>
                <div class="sidebar-title">培训系统</div>
            </div>
            <div class="sidebar-menu">
                <div class="menu-item" onclick="navigateTo('../dashboard/dashboard.html')">
                    <div class="menu-icon">📊</div>
                    <div class="menu-text">系统首页</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../user/user-list.html')">
                    <div class="menu-icon">👥</div>
                    <div class="menu-text">用户管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../course/course-list.html')">
                    <div class="menu-icon">📚</div>
                    <div class="menu-text">课程管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../instructor/instructor-list.html')">
                    <div class="menu-icon">👨‍🏫</div>
                    <div class="menu-text">教官管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../material/material-list.html')">
                    <div class="menu-icon">📄</div>
                    <div class="menu-text">教案管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../question/question-list.html')">
                    <div class="menu-icon">❓</div>
                    <div class="menu-text">考题管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../plan/plan-list.html')">
                    <div class="menu-icon">📋</div>
                    <div class="menu-text">培训计划</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../learning/learning-dashboard.html')">
                    <div class="menu-icon">🎓</div>
                    <div class="menu-text">在线学习</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../exam/exam-list.html')">
                    <div class="menu-icon">📝</div>
                    <div class="menu-text">在线考试</div>
                </div>
                <div class="menu-item active">
                    <div class="menu-icon">📁</div>
                    <div class="menu-text">文件管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../system/system-config.html')">
                    <div class="menu-icon">⚙️</div>
                    <div class="menu-text">系统设置</div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="header">
                <div class="breadcrumb">首页 / 文件管理 / 文件列表</div>
                <div class="user-info">
                    <div class="avatar">管</div>
                    <span>管理员</span>
                </div>
            </div>

            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">文件管理</h1>
                    <p class="page-description">统一管理系统文件，包括文件上传、存储管理、访问控制和下载统计</p>
                </div>

                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-icon">📁</div>
                        <div class="stat-value">1,234</div>
                        <div class="stat-label">总文件数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">💾</div>
                        <div class="stat-value">2.5GB</div>
                        <div class="stat-label">存储空间</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⬇️</div>
                        <div class="stat-value">5,678</div>
                        <div class="stat-label">下载次数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📈</div>
                        <div class="stat-value">89</div>
                        <div class="stat-label">今日上传</div>
                    </div>
                </div>

                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="showUploadModal()">+ 上传文件</button>
                        <button class="btn btn-success" onclick="createFolder()">新建文件夹</button>
                        <button class="btn btn-default" onclick="cleanupFiles()">清理文件</button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <select class="filter-select">
                                <option value="">全部类型</option>
                                <option value="image">图片</option>
                                <option value="document">文档</option>
                                <option value="video">视频</option>
                                <option value="archive">压缩包</option>
                                <option value="other">其他</option>
                            </select>
                            <select class="filter-select">
                                <option value="">全部大小</option>
                                <option value="small">小于1MB</option>
                                <option value="medium">1MB-10MB</option>
                                <option value="large">大于10MB</option>
                            </select>
                            <input type="text" class="search-input" placeholder="搜索文件名">
                            <button class="btn btn-primary">搜索</button>
                        </div>
                    </div>
                </div>

                <div class="file-table">
                    <table class="table" id="fileTable">
                        <thead>
                            <tr>
                                <th>文件信息</th>
                                <th>类型</th>
                                <th>大小</th>
                                <th>上传者</th>
                                <th>上传时间</th>
                                <th>下载次数</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="fileTableBody">
                            <!-- 文件数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">4</button>
                    <button class="page-btn">5</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 上传文件模态框 -->
    <div class="modal" id="uploadModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div class="modal-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; padding: 30px; width: 600px; max-width: 90%; max-height: 80vh; overflow-y: auto;">
            <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 class="modal-title">上传文件</h3>
                <button class="close-btn" onclick="closeUploadModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">&times;</button>
            </div>
            <form id="uploadForm">
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">选择文件 *</label>
                    <input type="file" class="form-input" name="files" multiple required style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    <small style="color: #666; font-size: 12px;">支持多文件上传，单个文件最大100MB</small>
                </div>
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">上传目录</label>
                    <select class="form-select" name="directory" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; background: white;">
                        <option value="/">根目录</option>
                        <option value="/courses">课程资料</option>
                        <option value="/materials">教案文件</option>
                        <option value="/exams">考试相关</option>
                        <option value="/temp">临时文件</option>
                    </select>
                </div>
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">文件描述</label>
                    <textarea class="form-textarea" name="description" placeholder="请输入文件描述（可选）" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; resize: vertical; min-height: 80px;"></textarea>
                </div>
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">访问权限</label>
                    <select class="form-select" name="permission" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; background: white;">
                        <option value="public">公开访问</option>
                        <option value="private">仅自己可见</option>
                        <option value="restricted">指定用户可见</option>
                    </select>
                </div>
            </form>
            <div class="modal-footer" style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;">
                <button class="btn btn-default" onclick="closeUploadModal()">取消</button>
                <button class="btn btn-primary" onclick="uploadFiles()">上传</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟文件数据
        const files = [
            {id: 1, name: 'Java基础教程.pdf', path: '/courses/java/', type: 'document', size: '2.3MB', uploader: '张教授', uploadTime: '2024-01-15 10:30', downloads: 156, extension: 'pdf'},
            {id: 2, name: '课程封面图.jpg', path: '/courses/images/', type: 'image', size: '856KB', uploader: '李老师', uploadTime: '2024-01-14 16:20', downloads: 45, extension: 'jpg'},
            {id: 3, name: '培训视频.mp4', path: '/materials/videos/', type: 'video', size: '125MB', uploader: '王工程师', uploadTime: '2024-01-13 09:15', downloads: 89, extension: 'mp4'},
            {id: 4, name: '项目源码.zip', path: '/materials/code/', type: 'archive', size: '15.2MB', uploader: '陈老师', uploadTime: '2024-01-12 14:45', downloads: 67, extension: 'zip'},
            {id: 5, name: '系统配置.txt', path: '/temp/', type: 'other', size: '1.2KB', uploader: '管理员', uploadTime: '2024-01-11 11:30', downloads: 12, extension: 'txt'},
            {id: 6, name: '考试题库.xlsx', path: '/exams/', type: 'document', size: '3.8MB', uploader: '张教授', uploadTime: '2024-01-10 08:20', downloads: 234, extension: 'xlsx'}
        ];

        function getFileIcon(extension) {
            const iconMap = {
                'pdf': '📄',
                'doc': '📝', 'docx': '📝',
                'xls': '📊', 'xlsx': '📊',
                'ppt': '📊', 'pptx': '📊',
                'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️',
                'mp4': '🎥', 'avi': '🎥', 'mov': '🎥',
                'mp3': '🎵', 'wav': '🎵',
                'zip': '📦', 'rar': '📦', '7z': '📦',
                'txt': '📄', 'md': '📄'
            };
            return iconMap[extension.toLowerCase()] || '📄';
        }

        function getFileTypeBadge(type) {
            const typeMap = {
                'image': { class: 'type-image', text: '图片' },
                'document': { class: 'type-document', text: '文档' },
                'video': { class: 'type-video', text: '视频' },
                'archive': { class: 'type-archive', text: '压缩包' },
                'other': { class: 'type-other', text: '其他' }
            };
            const typeInfo = typeMap[type] || { class: 'type-other', text: '未知' };
            return `<span class="file-type-badge ${typeInfo.class}">${typeInfo.text}</span>`;
        }

        function renderFileTable() {
            const tbody = document.getElementById('fileTableBody');
            tbody.innerHTML = '';

            files.forEach(file => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="file-info">
                            <span class="file-icon">${getFileIcon(file.extension)}</span>
                            <div class="file-details">
                                <div class="file-name">${file.name}</div>
                                <div class="file-path">${file.path}</div>
                            </div>
                        </div>
                    </td>
                    <td>${getFileTypeBadge(file.type)}</td>
                    <td class="file-size">${file.size}</td>
                    <td>${file.uploader}</td>
                    <td>${file.uploadTime}</td>
                    <td>${file.downloads}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-info btn-sm" onclick="downloadFile(${file.id})">下载</button>
                            <button class="btn btn-warning btn-sm" onclick="editFile(${file.id})">编辑</button>
                            <button class="btn btn-danger btn-sm" onclick="deleteFile(${file.id})">删除</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function navigateTo(url) {
            window.location.href = url;
        }

        function showUploadModal() {
            document.getElementById('uploadModal').style.display = 'block';
        }

        function closeUploadModal() {
            document.getElementById('uploadModal').style.display = 'none';
            document.getElementById('uploadForm').reset();
        }

        function uploadFiles() {
            const form = document.getElementById('uploadForm');
            const formData = new FormData(form);

            if (!formData.get('files') || formData.get('files').name === '') {
                alert('请选择要上传的文件！');
                return;
            }

            alert('文件上传成功！');
            closeUploadModal();
        }

        function downloadFile(id) {
            const file = files.find(f => f.id === id);
            if (file) {
                alert(`下载文件：${file.name}`);
            }
        }

        function editFile(id) {
            alert(`编辑文件信息 - ID: ${id}`);
        }

        function deleteFile(id) {
            if (confirm('确定要删除这个文件吗？删除后将无法恢复！')) {
                const index = files.findIndex(f => f.id === id);
                if (index > -1) {
                    files.splice(index, 1);
                    renderFileTable();
                    alert('文件删除成功！');
                }
            }
        }

        function createFolder() {
            const folderName = prompt('请输入文件夹名称：');
            if (folderName) {
                alert(`创建文件夹：${folderName}`);
            }
        }

        function cleanupFiles() {
            if (confirm('确定要清理临时文件和重复文件吗？')) {
                alert('文件清理完成！');
            }
        }

        // 页面加载时渲染文件表格
        window.onload = function() {
            renderFileTable();
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('uploadModal');
            if (event.target === modal) {
                closeUploadModal();
            }
        }
    </script>
</body>
</html>