<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教官管理 - 教育培训系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
        }
        
        .logo {
            width: 32px;
            height: 32px;
            background: #3498db;
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
        }
        
        .menu-item:hover {
            background: #34495e;
        }
        
        .menu-item.active {
            background: #3498db;
        }
        
        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .breadcrumb {
            color: #666;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }
        
        .page-description {
            color: #666;
        }
        
        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .toolbar-left {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .toolbar-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-default {
            background: #ecf0f1;
            color: #333;
            border: 1px solid #bdc3c7;
        }
        
        .btn-default:hover {
            background: #d5dbdb;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
        }
        
        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .instructor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .instructor-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .instructor-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .instructor-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        
        .instructor-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin: 0 auto 15px;
            border: 3px solid rgba(255,255,255,0.3);
        }
        
        .instructor-name {
            font-size: 20px;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .instructor-code {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .instructor-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: rgba(39, 174, 96, 0.9);
            color: white;
        }
        
        .status-inactive {
            background: rgba(231, 76, 60, 0.9);
            color: white;
        }
        
        .instructor-content {
            padding: 20px;
        }
        
        .instructor-info {
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .info-label {
            color: #666;
            font-weight: 500;
        }
        
        .info-value {
            color: #333;
        }
        
        .speciality-tags {
            margin-bottom: 15px;
        }
        
        .tag {
            display: inline-block;
            background: #e8f4fd;
            color: #3498db;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin: 2px;
        }
        
        .instructor-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 15px;
            padding: 15px 0;
            border-top: 1px solid #f0f0f0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        
        .instructor-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-warning:hover {
            background: #e67e22;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .rating {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .stars {
            color: #f39c12;
        }
        
        .rating-score {
            font-weight: bold;
            color: #333;
        }
        
        .pagination {
            margin-top: 30px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            color: #333;
        }
        
        .page-btn:hover {
            background: #f0f0f0;
        }
        
        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">T</div>
                <div class="sidebar-title">培训系统</div>
            </div>
            <div class="sidebar-menu">
                <div class="menu-item" onclick="navigateTo('../dashboard/dashboard.html')">
                    <div class="menu-icon">📊</div>
                    <div class="menu-text">系统首页</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../user/user-list.html')">
                    <div class="menu-icon">👥</div>
                    <div class="menu-text">用户管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../course/course-list.html')">
                    <div class="menu-icon">📚</div>
                    <div class="menu-text">课程管理</div>
                </div>
                <div class="menu-item active">
                    <div class="menu-icon">👨‍🏫</div>
                    <div class="menu-text">教官管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../plan/plan-list.html')">
                    <div class="menu-icon">📋</div>
                    <div class="menu-text">培训计划</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../learning/learning-dashboard.html')">
                    <div class="menu-icon">🎓</div>
                    <div class="menu-text">在线学习</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../exam/exam-list.html')">
                    <div class="menu-icon">📝</div>
                    <div class="menu-text">在线考试</div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="breadcrumb">首页 / 教官管理 / 教官列表</div>
                <div class="user-info">
                    <div class="avatar">管</div>
                    <span>管理员</span>
                </div>
            </div>
            
            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">教官管理</h1>
                    <p class="page-description">管理培训教官信息，包括教官认证、资质管理和教学评价</p>
                </div>
                
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="addInstructor()">+ 新增教官</button>
                        <button class="btn btn-default" onclick="exportInstructors()">导出数据</button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <select class="filter-select">
                                <option value="">全部专业</option>
                                <option value="Java">Java开发</option>
                                <option value="Python">Python开发</option>
                                <option value="前端">前端开发</option>
                                <option value="数据库">数据库</option>
                            </select>
                            <select class="filter-select">
                                <option value="">全部状态</option>
                                <option value="1">在职</option>
                                <option value="0">离职</option>
                            </select>
                            <input type="text" class="search-input" placeholder="搜索教官姓名">
                            <button class="btn btn-primary">搜索</button>
                        </div>
                    </div>
                </div>
                
                <div class="instructor-grid" id="instructorGrid">
                    <!-- 教官卡片将通过JavaScript动态生成 -->
                </div>
                
                <div class="pagination">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟教官数据
        const instructors = [
            {
                id: 1,
                name: '张老师',
                code: 'INST_001',
                speciality: ['Java开发', 'Spring框架', '微服务'],
                experienceYears: 8,
                rating: 4.8,
                totalCourses: 15,
                totalStudents: 456,
                status: 1,
                title: '高级工程师',
                phone: '13800138001'
            },
            {
                id: 2,
                name: '李老师',
                code: 'INST_002',
                speciality: ['Python开发', '数据分析', '机器学习'],
                experienceYears: 6,
                rating: 4.6,
                totalCourses: 12,
                totalStudents: 328,
                status: 1,
                title: '高级工程师',
                phone: '13800138002'
            },
            {
                id: 3,
                name: '王老师',
                code: 'INST_003',
                speciality: ['前端开发', 'Vue.js', 'React'],
                experienceYears: 5,
                rating: 4.7,
                totalCourses: 10,
                totalStudents: 289,
                status: 1,
                title: '中级工程师',
                phone: '13800138003'
            },
            {
                id: 4,
                name: '赵老师',
                code: 'INST_004',
                speciality: ['数据库设计', 'MySQL', 'PostgreSQL'],
                experienceYears: 10,
                rating: 4.9,
                totalCourses: 8,
                totalStudents: 234,
                status: 0,
                title: '资深工程师',
                phone: '13800138004'
            }
        ];
        
        function renderInstructorGrid() {
            const grid = document.getElementById('instructorGrid');
            grid.innerHTML = '';
            
            instructors.forEach(instructor => {
                const statusClass = instructor.status === 1 ? 'status-active' : 'status-inactive';
                const statusText = instructor.status === 1 ? '在职' : '离职';
                
                const card = document.createElement('div');
                card.className = 'instructor-card';
                card.innerHTML = `
                    <div class="instructor-header">
                        <div class="instructor-status ${statusClass}">${statusText}</div>
                        <div class="instructor-avatar">👨‍🏫</div>
                        <div class="instructor-name">${instructor.name}</div>
                        <div class="instructor-code">${instructor.code}</div>
                    </div>
                    <div class="instructor-content">
                        <div class="instructor-info">
                            <div class="info-item">
                                <span class="info-label">职称：</span>
                                <span class="info-value">${instructor.title}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">经验：</span>
                                <span class="info-value">${instructor.experienceYears}年</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">评分：</span>
                                <div class="rating">
                                    <span class="stars">★★★★★</span>
                                    <span class="rating-score">${instructor.rating}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="speciality-tags">
                            ${instructor.speciality.map(spec => `<span class="tag">${spec}</span>`).join('')}
                        </div>
                        
                        <div class="instructor-stats">
                            <div class="stat-item">
                                <div class="stat-number">${instructor.totalCourses}</div>
                                <div class="stat-label">授课数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${instructor.totalStudents}</div>
                                <div class="stat-label">学员数</div>
                            </div>
                        </div>
                        
                        <div class="instructor-actions">
                            <button class="btn btn-info btn-sm" onclick="viewInstructor(${instructor.id})">查看</button>
                            <button class="btn btn-warning btn-sm" onclick="editInstructor(${instructor.id})">编辑</button>
                            <button class="btn btn-danger btn-sm" onclick="deleteInstructor(${instructor.id})">删除</button>
                        </div>
                    </div>
                `;
                grid.appendChild(card);
            });
        }
        
        function navigateTo(url) {
            window.location.href = url;
        }
        
        function addInstructor() {
            alert('新增教官功能开发中...');
        }
        
        function viewInstructor(id) {
            alert(`查看教官详情 - ID: ${id}`);
        }
        
        function editInstructor(id) {
            alert(`编辑教官信息 - ID: ${id}`);
        }
        
        function deleteInstructor(id) {
            if (confirm('确定要删除这个教官吗？')) {
                const index = instructors.findIndex(i => i.id === id);
                if (index > -1) {
                    instructors.splice(index, 1);
                    renderInstructorGrid();
                    alert('教官删除成功！');
                }
            }
        }
        
        function exportInstructors() {
            alert('导出功能开发中...');
        }
        
        // 页面加载时渲染教官网格
        window.onload = function() {
            renderInstructorGrid();
        }
    </script>
</body>
</html>
