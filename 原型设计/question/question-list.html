<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考题管理 - 教育培训系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
        }

        .layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
        }

        .logo {
            width: 32px;
            height: 32px;
            background: #3498db;
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 500;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
        }

        .menu-item:hover {
            background: #34495e;
        }

        .menu-item.active {
            background: #3498db;
        }

        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .breadcrumb {
            color: #666;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }

        .page-description {
            color: #666;
        }

        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .toolbar-left {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .toolbar-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-default {
            background: #ecf0f1;
            color: #333;
            border: 1px solid #bdc3c7;
        }

        .btn-default:hover {
            background: #d5dbdb;
        }

        .search-box {
            display: flex;
            gap: 10px;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
        }

        .question-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
            transition: all 0.3s ease;
        }

        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .question-type {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-single {
            background: #e8f4fd;
            color: #3498db;
        }

        .type-multiple {
            background: #fff3cd;
            color: #856404;
        }

        .type-judge {
            background: #d4edda;
            color: #155724;
        }

        .type-fill {
            background: #f8d7da;
            color: #721c24;
        }

        .question-difficulty {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .difficulty-easy {
            background: #d4edda;
            color: #155724;
        }

        .difficulty-medium {
            background: #fff3cd;
            color: #856404;
        }

        .difficulty-hard {
            background: #f8d7da;
            color: #721c24;
        }

        .question-content {
            font-size: 16px;
            line-height: 1.5;
            color: #333;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .question-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;
            color: #666;
        }

        .question-actions {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .pagination {
            margin-top: 30px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            color: #333;
        }

        .page-btn:hover {
            background: #f0f0f0;
        }

        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">T</div>
                <div class="sidebar-title">培训系统</div>
            </div>
            <div class="sidebar-menu">
                <div class="menu-item" onclick="navigateTo('../dashboard/dashboard.html')">
                    <div class="menu-icon">📊</div>
                    <div class="menu-text">系统首页</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../user/user-list.html')">
                    <div class="menu-icon">👥</div>
                    <div class="menu-text">用户管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../course/course-list.html')">
                    <div class="menu-icon">📚</div>
                    <div class="menu-text">课程管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../instructor/instructor-list.html')">
                    <div class="menu-icon">👨‍🏫</div>
                    <div class="menu-text">教官管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../material/material-list.html')">
                    <div class="menu-icon">📄</div>
                    <div class="menu-text">教案管理</div>
                </div>
                <div class="menu-item active">
                    <div class="menu-icon">❓</div>
                    <div class="menu-text">考题管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../plan/plan-list.html')">
                    <div class="menu-icon">📋</div>
                    <div class="menu-text">培训计划</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../learning/learning-dashboard.html')">
                    <div class="menu-icon">🎓</div>
                    <div class="menu-text">在线学习</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../exam/exam-list.html')">
                    <div class="menu-icon">📝</div>
                    <div class="menu-text">在线考试</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../file/file-list.html')">
                    <div class="menu-icon">📁</div>
                    <div class="menu-text">文件管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../system/system-config.html')">
                    <div class="menu-icon">⚙️</div>
                    <div class="menu-text">系统设置</div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="header">
                <div class="breadcrumb">首页 / 考题管理 / 题目列表</div>
                <div class="user-info">
                    <div class="avatar">管</div>
                    <span>管理员</span>
                </div>
            </div>

            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">考题管理</h1>
                    <p class="page-description">管理考试题目，包括题目录入、分类管理、难度设置和题库维护</p>
                </div>

                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="showAddModal()">+ 新增题目</button>
                        <button class="btn btn-success" onclick="importQuestions()">批量导入</button>
                        <button class="btn btn-default" onclick="exportQuestions()">导出题库</button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <select class="filter-select">
                                <option value="">全部课程</option>
                                <option value="1">Java基础编程</option>
                                <option value="2">Spring Boot实战</option>
                                <option value="3">MySQL数据库设计</option>
                                <option value="4">Vue.js前端开发</option>
                            </select>
                            <select class="filter-select">
                                <option value="">全部类型</option>
                                <option value="single">单选题</option>
                                <option value="multiple">多选题</option>
                                <option value="judge">判断题</option>
                                <option value="fill">填空题</option>
                            </select>
                            <select class="filter-select">
                                <option value="">全部难度</option>
                                <option value="easy">简单</option>
                                <option value="medium">中等</option>
                                <option value="hard">困难</option>
                            </select>
                            <input type="text" class="search-input" placeholder="搜索题目内容">
                            <button class="btn btn-primary">搜索</button>
                        </div>
                    </div>
                </div>

                <div class="question-grid" id="questionGrid">
                    <!-- 题目卡片将通过JavaScript动态生成 -->
                </div>

                <div class="pagination">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">4</button>
                    <button class="page-btn">5</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑题目模态框 -->
    <div class="modal" id="questionModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div class="modal-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; padding: 30px; width: 800px; max-width: 90%; max-height: 80vh; overflow-y: auto;">
            <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 class="modal-title" id="modalTitle">新增题目</h3>
                <button class="close-btn" onclick="closeModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">&times;</button>
            </div>
            <form id="questionForm">
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">题目类型 *</label>
                    <select class="form-select" name="questionType" required onchange="handleTypeChange()" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; background: white;">
                        <option value="">请选择题目类型</option>
                        <option value="single">单选题</option>
                        <option value="multiple">多选题</option>
                        <option value="judge">判断题</option>
                        <option value="fill">填空题</option>
                    </select>
                </div>
                <div class="form-row" style="display: flex; gap: 15px;">
                    <div class="form-col" style="flex: 1;">
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">所属课程 *</label>
                            <select class="form-select" name="courseId" required style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; background: white;">
                                <option value="">请选择课程</option>
                                <option value="1">Java基础编程</option>
                                <option value="2">Spring Boot实战</option>
                                <option value="3">MySQL数据库设计</option>
                                <option value="4">Vue.js前端开发</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col" style="flex: 1;">
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">难度等级 *</label>
                            <select class="form-select" name="difficulty" required style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; background: white;">
                                <option value="">请选择难度</option>
                                <option value="easy">简单</option>
                                <option value="medium">中等</option>
                                <option value="hard">困难</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">题目内容 *</label>
                    <textarea class="form-textarea" name="questionContent" required placeholder="请输入题目内容" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; resize: vertical; min-height: 100px;"></textarea>
                </div>
                <div id="optionsContainer" style="display: none;">
                    <div class="form-group" style="margin-bottom: 15px;">
                        <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">选项设置</label>
                        <div id="optionsList">
                            <!-- 选项将动态生成 -->
                        </div>
                        <button type="button" class="btn btn-default" onclick="addOption()" style="margin-top: 10px;">+ 添加选项</button>
                    </div>
                </div>
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">正确答案 *</label>
                    <input type="text" class="form-input" name="correctAnswer" required placeholder="请输入正确答案" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    <small style="color: #666; font-size: 12px;">单选题填写选项字母(如A)，多选题用逗号分隔(如A,C)，判断题填写true/false，填空题填写答案</small>
                </div>
                <div class="form-group" style="margin-bottom: 15px;">
                    <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">解析说明</label>
                    <textarea class="form-textarea" name="explanation" placeholder="请输入答案解析" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; resize: vertical; min-height: 80px;"></textarea>
                </div>
                <div class="form-row" style="display: flex; gap: 15px;">
                    <div class="form-col" style="flex: 1;">
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">分值</label>
                            <input type="number" class="form-input" name="score" min="1" max="100" value="5" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                        </div>
                    </div>
                    <div class="form-col" style="flex: 1;">
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label class="form-label" style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">标签</label>
                            <input type="text" class="form-input" name="tags" placeholder="用逗号分隔多个标签" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                        </div>
                    </div>
                </div>
            </form>
            <div class="modal-footer" style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;">
                <button class="btn btn-default" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" onclick="saveQuestion()">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟题目数据
        const questions = [
            {id: 1, content: 'Java中哪个关键字用于定义类？', type: 'single', course: 'Java基础编程', difficulty: 'easy', score: 5, creator: '张教授', createTime: '2024-01-15', usageCount: 23},
            {id: 2, content: 'Spring Boot的主要优势包括哪些？（多选）', type: 'multiple', course: 'Spring Boot实战', difficulty: 'medium', score: 10, creator: '李老师', createTime: '2024-01-12', usageCount: 15},
            {id: 3, content: 'MySQL支持事务处理。', type: 'judge', course: 'MySQL数据库设计', difficulty: 'easy', score: 3, creator: '王工程师', createTime: '2024-01-10', usageCount: 31},
            {id: 4, content: 'Vue.js中用于双向数据绑定的指令是______。', type: 'fill', course: 'Vue.js前端开发', difficulty: 'medium', score: 8, creator: '陈老师', createTime: '2024-01-08', usageCount: 19},
            {id: 5, content: '以下哪些是Java的基本数据类型？（多选）', type: 'multiple', course: 'Java基础编程', difficulty: 'hard', score: 15, creator: '张教授', createTime: '2024-01-05', usageCount: 8}
        ];

        function getTypeInfo(type) {
            const typeMap = {
                'single': { class: 'type-single', text: '单选题' },
                'multiple': { class: 'type-multiple', text: '多选题' },
                'judge': { class: 'type-judge', text: '判断题' },
                'fill': { class: 'type-fill', text: '填空题' }
            };
            return typeMap[type] || { class: 'type-single', text: '未知' };
        }

        function getDifficultyInfo(difficulty) {
            const difficultyMap = {
                'easy': { class: 'difficulty-easy', text: '简单' },
                'medium': { class: 'difficulty-medium', text: '中等' },
                'hard': { class: 'difficulty-hard', text: '困难' }
            };
            return difficultyMap[difficulty] || { class: 'difficulty-easy', text: '未知' };
        }

        function renderQuestionGrid() {
            const grid = document.getElementById('questionGrid');
            grid.innerHTML = '';

            questions.forEach(question => {
                const typeInfo = getTypeInfo(question.type);
                const difficultyInfo = getDifficultyInfo(question.difficulty);

                const card = document.createElement('div');
                card.className = 'question-card';
                card.innerHTML = `
                    <div class="question-header">
                        <div>
                            <span class="question-type ${typeInfo.class}">${typeInfo.text}</span>
                        </div>
                        <span class="question-difficulty ${difficultyInfo.class}">${difficultyInfo.text}</span>
                    </div>
                    <div class="question-content">${question.content}</div>
                    <div class="question-meta">
                        <div>
                            <span>课程：${question.course}</span> |
                            <span>分值：${question.score}分</span>
                        </div>
                        <div>
                            <span>使用：${question.usageCount}次</span>
                        </div>
                    </div>
                    <div class="question-meta">
                        <span>创建者：${question.creator}</span>
                        <span>${question.createTime}</span>
                    </div>
                    <div class="question-actions">
                        <button class="btn btn-info btn-sm" onclick="viewQuestion(${question.id})">查看</button>
                        <button class="btn btn-warning btn-sm" onclick="editQuestion(${question.id})">编辑</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteQuestion(${question.id})">删除</button>
                    </div>
                `;
                grid.appendChild(card);
            });
        }

        function navigateTo(url) {
            window.location.href = url;
        }

        function showAddModal() {
            document.getElementById('modalTitle').textContent = '新增题目';
            document.getElementById('questionForm').reset();
            document.getElementById('optionsContainer').style.display = 'none';
            document.getElementById('questionModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('questionModal').style.display = 'none';
        }

        function handleTypeChange() {
            const type = document.querySelector('[name="questionType"]').value;
            const optionsContainer = document.getElementById('optionsContainer');

            if (type === 'single' || type === 'multiple') {
                optionsContainer.style.display = 'block';
                generateOptions();
            } else {
                optionsContainer.style.display = 'none';
            }
        }

        function generateOptions() {
            const optionsList = document.getElementById('optionsList');
            optionsList.innerHTML = '';

            for (let i = 0; i < 4; i++) {
                const optionDiv = document.createElement('div');
                optionDiv.style.cssText = 'display: flex; align-items: center; gap: 10px; margin-bottom: 10px;';
                optionDiv.innerHTML = `
                    <span style="width: 20px; font-weight: bold;">${String.fromCharCode(65 + i)}:</span>
                    <input type="text" class="form-input" name="option${String.fromCharCode(65 + i)}" placeholder="请输入选项内容" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeOption(this)">删除</button>
                `;
                optionsList.appendChild(optionDiv);
            }
        }

        function addOption() {
            const optionsList = document.getElementById('optionsList');
            const optionCount = optionsList.children.length;
            const letter = String.fromCharCode(65 + optionCount);

            const optionDiv = document.createElement('div');
            optionDiv.style.cssText = 'display: flex; align-items: center; gap: 10px; margin-bottom: 10px;';
            optionDiv.innerHTML = `
                <span style="width: 20px; font-weight: bold;">${letter}:</span>
                <input type="text" class="form-input" name="option${letter}" placeholder="请输入选项内容" style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeOption(this)">删除</button>
            `;
            optionsList.appendChild(optionDiv);
        }

        function removeOption(button) {
            button.parentElement.remove();
        }

        function saveQuestion() {
            const form = document.getElementById('questionForm');
            const formData = new FormData(form);

            if (!formData.get('questionType') || !formData.get('courseId') || !formData.get('difficulty') || !formData.get('questionContent') || !formData.get('correctAnswer')) {
                alert('请填写必填字段！');
                return;
            }

            alert('题目保存成功！');
            closeModal();
        }

        function viewQuestion(id) {
            alert(`查看题目详情 - ID: ${id}`);
        }

        function editQuestion(id) {
            const question = questions.find(q => q.id === id);
            if (question) {
                document.getElementById('modalTitle').textContent = '编辑题目';
                document.getElementById('questionModal').style.display = 'block';
            }
        }

        function deleteQuestion(id) {
            if (confirm('确定要删除这个题目吗？删除后将无法恢复！')) {
                const index = questions.findIndex(q => q.id === id);
                if (index > -1) {
                    questions.splice(index, 1);
                    renderQuestionGrid();
                    alert('题目删除成功！');
                }
            }
        }

        function importQuestions() {
            alert('批量导入功能开发中...');
        }

        function exportQuestions() {
            alert('导出功能开发中...');
        }

        // 页面加载时渲染题目网格
        window.onload = function() {
            renderQuestionGrid();
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('questionModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>