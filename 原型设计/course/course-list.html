<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程管理 - 教育培训系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
        }

        .layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
        }

        .logo {
            width: 32px;
            height: 32px;
            background: #3498db;
            border-radius: 6px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 500;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
        }

        .menu-item:hover {
            background: #34495e;
        }

        .menu-item.active {
            background: #3498db;
        }

        .menu-icon {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .breadcrumb {
            color: #666;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }

        .page-description {
            color: #666;
        }

        .toolbar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .toolbar-left {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .toolbar-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-default {
            background: #ecf0f1;
            color: #333;
            border: 1px solid #bdc3c7;
        }

        .btn-default:hover {
            background: #d5dbdb;
        }

        .search-box {
            display: flex;
            gap: 10px;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }

        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        .course-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
        }

        .course-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .course-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .course-cover {
            height: 180px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
            position: relative;
        }

        .course-status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: rgba(39, 174, 96, 0.9);
            color: white;
        }

        .status-draft {
            background: rgba(241, 196, 15, 0.9);
            color: white;
        }

        .status-inactive {
            background: rgba(231, 76, 60, 0.9);
            color: white;
        }

        .course-content {
            padding: 20px;
        }

        .course-title {
            font-size: 18px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .course-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
            color: #666;
        }

        .course-level {
            background: #e8f4fd;
            color: #3498db;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }

        .course-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .course-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 14px;
            color: #666;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .course-actions {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 30px;
            width: 600px;
            max-width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 500;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
        }

        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-col {
            flex: 1;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination {
            margin-top: 30px;
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            color: #333;
        }

        .page-btn:hover {
            background: #f0f0f0;
        }

        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
    </style>
</head>
<body>
    <div class="layout">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">T</div>
                <div class="sidebar-title">培训系统</div>
            </div>
            <div class="sidebar-menu">
                <div class="menu-item" onclick="navigateTo('../dashboard/dashboard.html')">
                    <div class="menu-icon">📊</div>
                    <div class="menu-text">系统首页</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../user/user-list.html')">
                    <div class="menu-icon">👥</div>
                    <div class="menu-text">用户管理</div>
                </div>
                <div class="menu-item active">
                    <div class="menu-icon">📚</div>
                    <div class="menu-text">课程管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../instructor/instructor-list.html')">
                    <div class="menu-icon">👨‍🏫</div>
                    <div class="menu-text">教官管理</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../plan/plan-list.html')">
                    <div class="menu-icon">📋</div>
                    <div class="menu-text">培训计划</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../learning/learning-dashboard.html')">
                    <div class="menu-icon">🎓</div>
                    <div class="menu-text">在线学习</div>
                </div>
                <div class="menu-item" onclick="navigateTo('../exam/exam-list.html')">
                    <div class="menu-icon">📝</div>
                    <div class="menu-text">在线考试</div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="header">
                <div class="breadcrumb">首页 / 课程管理 / 课程列表</div>
                <div class="user-info">
                    <div class="avatar">管</div>
                    <span>管理员</span>
                </div>
            </div>

            <div class="content">
                <div class="page-header">
                    <h1 class="page-title">课程管理</h1>
                    <p class="page-description">管理培训课程信息，包括课程创建、编辑、分类和状态管理</p>
                </div>

                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="showAddModal()">+ 新增课程</button>
                        <button class="btn btn-success" onclick="batchPublish()">批量发布</button>
                        <button class="btn btn-default" onclick="exportCourses()">导出数据</button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <select class="filter-select">
                                <option value="">全部分类</option>
                                <option value="1">编程语言</option>
                                <option value="2">框架技术</option>
                                <option value="3">数据库</option>
                                <option value="4">项目管理</option>
                            </select>
                            <select class="filter-select">
                                <option value="">全部级别</option>
                                <option value="初级">初级</option>
                                <option value="中级">中级</option>
                                <option value="高级">高级</option>
                            </select>
                            <select class="filter-select">
                                <option value="">全部状态</option>
                                <option value="1">已发布</option>
                                <option value="2">草稿</option>
                                <option value="0">已下线</option>
                            </select>
                            <input type="text" class="search-input" placeholder="搜索课程名称">
                            <button class="btn btn-primary">搜索</button>
                        </div>
                    </div>
                </div>

                <div class="course-grid" id="courseGrid">
                    <!-- 课程卡片将通过JavaScript动态生成 -->
                </div>

                <div class="pagination">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">4</button>
                    <button class="page-btn">5</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑课程模态框 -->
    <div class="modal" id="courseModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">新增课程</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <form id="courseForm">
                <div class="form-group">
                    <label class="form-label">课程名称 *</label>
                    <input type="text" class="form-input" name="courseName" required>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label">课程编码 *</label>
                            <input type="text" class="form-input" name="courseCode" required>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label">课程分类</label>
                            <select class="form-select" name="categoryId">
                                <option value="">请选择分类</option>
                                <option value="1">编程语言</option>
                                <option value="2">框架技术</option>
                                <option value="3">数据库</option>
                                <option value="4">项目管理</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label">课程级别</label>
                            <select class="form-select" name="courseLevel">
                                <option value="">请选择级别</option>
                                <option value="初级">初级</option>
                                <option value="中级">中级</option>
                                <option value="高级">高级</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label">课程时长(小时)</label>
                            <input type="number" class="form-input" name="duration" min="1">
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label">学分</label>
                            <input type="number" class="form-input" name="creditHours" step="0.5" min="0">
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label">是否必修</label>
                            <select class="form-select" name="isRequired">
                                <option value="false">选修</option>
                                <option value="true">必修</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">课程描述</label>
                    <textarea class="form-textarea" name="description" placeholder="请输入课程描述"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">学习目标</label>
                    <textarea class="form-textarea" name="objectives" placeholder="请输入学习目标"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">前置要求</label>
                    <textarea class="form-textarea" name="prerequisites" placeholder="请输入前置要求"></textarea>
                </div>
            </form>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" onclick="saveCourse()">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟课程数据
        const courses = [
            {id: 1, courseName: 'Java基础编程', courseCode: 'JAVA_BASIC_001', category: '编程语言', level: '初级', duration: 40, creditHours: 2.5, description: 'Java编程语言基础课程，适合初学者', status: 1, studentCount: 156, materialCount: 12, isRequired: true},
            {id: 2, courseName: 'Spring Boot实战', courseCode: 'SPRING_BOOT_001', category: '框架技术', level: '中级', duration: 60, creditHours: 3.0, description: 'Spring Boot框架实战开发课程', status: 1, studentCount: 89, materialCount: 18, isRequired: false},
            {id: 3, courseName: 'MySQL数据库设计', courseCode: 'MYSQL_001', category: '数据库', level: '中级', duration: 32, creditHours: 2.0, description: 'MySQL数据库设计与优化', status: 2, studentCount: 0, materialCount: 8, isRequired: false},
            {id: 4, courseName: 'Vue.js前端开发', courseCode: 'VUE_001', category: '框架技术', level: '中级', duration: 48, creditHours: 2.5, description: 'Vue.js前端框架开发实战', status: 1, studentCount: 234, materialCount: 15, isRequired: true},
            {id: 5, courseName: '项目管理基础', courseCode: 'PM_BASIC_001', category: '项目管理', level: '初级', duration: 24, creditHours: 1.5, description: '项目管理基础知识和实践', status: 0, studentCount: 67, materialCount: 6, isRequired: false},
            {id: 6, courseName: 'Python数据分析', courseCode: 'PYTHON_DATA_001', category: '编程语言', level: '高级', duration: 56, creditHours: 3.5, description: 'Python数据分析与可视化', status: 1, studentCount: 123, materialCount: 20, isRequired: false}
        ];

        function renderCourseGrid() {
            const grid = document.getElementById('courseGrid');
            grid.innerHTML = '';

            courses.forEach(course => {
                const statusClass = course.status === 1 ? 'status-active' : course.status === 2 ? 'status-draft' : 'status-inactive';
                const statusText = course.status === 1 ? '已发布' : course.status === 2 ? '草稿' : '已下线';

                const card = document.createElement('div');
                card.className = 'course-card';
                card.innerHTML = `
                    <div class="course-cover">
                        <div class="course-status ${statusClass}">${statusText}</div>
                        📚
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">${course.courseName}</h3>
                        <div class="course-meta">
                            <span class="course-level">${course.level}</span>
                            <span>${course.duration}小时 | ${course.creditHours}学分</span>
                        </div>
                        <p class="course-description">${course.description}</p>
                        <div class="course-stats">
                            <div class="stat-item">
                                <span>👥</span>
                                <span>${course.studentCount}人学习</span>
                            </div>
                            <div class="stat-item">
                                <span>📄</span>
                                <span>${course.materialCount}个教案</span>
                            </div>
                            <div class="stat-item">
                                <span>${course.isRequired ? '🔒' : '🔓'}</span>
                                <span>${course.isRequired ? '必修' : '选修'}</span>
                            </div>
                        </div>
                        <div class="course-actions">
                            <button class="btn btn-info btn-sm" onclick="viewCourse(${course.id})">查看</button>
                            <button class="btn btn-warning btn-sm" onclick="editCourse(${course.id})">编辑</button>
                            <button class="btn btn-danger btn-sm" onclick="deleteCourse(${course.id})">删除</button>
                        </div>
                    </div>
                `;
                grid.appendChild(card);
            });
        }

        function navigateTo(url) {
            window.location.href = url;
        }

        function showAddModal() {
            document.getElementById('modalTitle').textContent = '新增课程';
            document.getElementById('courseForm').reset();
            document.getElementById('courseModal').style.display = 'block';
        }

        function editCourse(id) {
            const course = courses.find(c => c.id === id);
            if (course) {
                document.getElementById('modalTitle').textContent = '编辑课程';
                const form = document.getElementById('courseForm');
                form.courseName.value = course.courseName;
                form.courseCode.value = course.courseCode;
                form.courseLevel.value = course.level;
                form.duration.value = course.duration;
                form.creditHours.value = course.creditHours;
                form.description.value = course.description;
                form.isRequired.value = course.isRequired;
                document.getElementById('courseModal').style.display = 'block';
            }
        }

        function viewCourse(id) {
            alert(`查看课程详情 - ID: ${id}`);
        }

        function deleteCourse(id) {
            if (confirm('确定要删除这个课程吗？删除后将无法恢复！')) {
                const index = courses.findIndex(c => c.id === id);
                if (index > -1) {
                    courses.splice(index, 1);
                    renderCourseGrid();
                    alert('课程删除成功！');
                }
            }
        }

        function closeModal() {
            document.getElementById('courseModal').style.display = 'none';
        }

        function saveCourse() {
            const form = document.getElementById('courseForm');
            const formData = new FormData(form);

            if (!formData.get('courseName') || !formData.get('courseCode')) {
                alert('请填写必填字段！');
                return;
            }

            alert('课程保存成功！');
            closeModal();
        }

        function batchPublish() {
            alert('批量发布功能开发中...');
        }

        function exportCourses() {
            alert('导出功能开发中...');
        }

        // 页面加载时渲染课程网格
        window.onload = function() {
            renderCourseGrid();
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('courseModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
