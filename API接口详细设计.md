# 教育培训系统API接口详细设计

## 一、接口设计规范

### 1.1 RESTful API设计原则
- 使用HTTP动词表示操作：GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- URL路径使用名词复数形式
- 统一的响应格式
- 合理的HTTP状态码使用
- API版本控制：/api/v1/

### 1.2 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00Z",
  "traceId": "trace-uuid-12345"
}
```

### 1.3 分页响应格式
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [],
    "total": 100,
    "size": 20,
    "current": 1,
    "pages": 5
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 1.4 错误响应格式
```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": null,
  "errors": [
    {
      "field": "username",
      "message": "用户名不能为空"
    }
  ],
  "timestamp": "2024-01-01T12:00:00Z",
  "traceId": "trace-uuid-12345"
}
```

## 二、用户认证相关API

### 2.1 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456",
  "captcha": "ABCD",
  "captchaKey": "captcha-key-123"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh-token-123",
    "expiresIn": 7200,
    "user": {
      "id": 1,
      "username": "admin",
      "realName": "管理员",
      "avatar": "http://example.com/avatar.jpg",
      "roles": ["ADMIN"],
      "permissions": ["user:read", "user:write"]
    }
  }
}
```

### 2.2 获取当前用户信息
```http
GET /api/v1/auth/userinfo
Authorization: Bearer {token}
```

### 2.3 刷新Token
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refreshToken": "refresh-token-123"
}
```

### 2.4 用户登出
```http
POST /api/v1/auth/logout
Authorization: Bearer {token}
```

## 三、用户管理API

### 3.1 用户列表查询
```http
GET /api/v1/users?page=1&size=20&keyword=张三&departmentId=1&status=1
Authorization: Bearer {token}
```

**查询参数：**
- `page`: 页码，默认1
- `size`: 每页大小，默认20
- `keyword`: 关键词搜索(姓名、用户名)
- `departmentId`: 部门ID
- `status`: 用户状态(1-正常，0-禁用)

### 3.2 创建用户
```http
POST /api/v1/users
Authorization: Bearer {token}
Content-Type: application/json

{
  "username": "zhangsan",
  "password": "123456",
  "realName": "张三",
  "phone": "13800138000",
  "email": "<EMAIL>",
  "departmentId": 1,
  "position": "工程师",
  "employeeNo": "EMP001",
  "roleIds": [2, 3]
}
```

### 3.3 更新用户
```http
PUT /api/v1/users/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "realName": "张三丰",
  "phone": "13800138001",
  "email": "<EMAIL>",
  "departmentId": 2,
  "position": "高级工程师",
  "status": 1
}
```

### 3.4 删除用户
```http
DELETE /api/v1/users/{id}
Authorization: Bearer {token}
```

### 3.5 批量删除用户
```http
DELETE /api/v1/users/batch
Authorization: Bearer {token}
Content-Type: application/json

{
  "ids": [1, 2, 3]
}
```

## 四、课程管理API

### 4.1 课程列表查询
```http
GET /api/v1/courses?page=1&size=20&keyword=Java&categoryId=1&level=初级&status=1
Authorization: Bearer {token}
```

### 4.2 创建课程
```http
POST /api/v1/courses
Authorization: Bearer {token}
Content-Type: application/json

{
  "courseName": "Java基础编程",
  "courseCode": "JAVA_BASIC_001",
  "categoryId": 1,
  "courseLevel": "初级",
  "duration": 40,
  "creditHours": 2.5,
  "description": "Java编程语言基础课程",
  "objectives": "掌握Java基本语法和面向对象编程",
  "prerequisites": "计算机基础知识",
  "coverImageUrl": "http://example.com/cover.jpg",
  "isRequired": true
}
```

### 4.3 更新课程
```http
PUT /api/v1/courses/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "courseName": "Java高级编程",
  "description": "Java高级特性和框架应用",
  "duration": 60,
  "status": 1
}
```

### 4.4 课程详情
```http
GET /api/v1/courses/{id}
Authorization: Bearer {token}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "courseName": "Java基础编程",
    "courseCode": "JAVA_BASIC_001",
    "category": {
      "id": 1,
      "categoryName": "编程语言",
      "categoryCode": "PROGRAMMING"
    },
    "courseLevel": "初级",
    "duration": 40,
    "creditHours": 2.5,
    "description": "Java编程语言基础课程",
    "objectives": "掌握Java基本语法和面向对象编程",
    "prerequisites": "计算机基础知识",
    "coverImageUrl": "http://example.com/cover.jpg",
    "isRequired": true,
    "status": 1,
    "createTime": "2024-01-01T10:00:00Z",
    "updateTime": "2024-01-01T10:00:00Z"
  }
}
```

## 五、教官管理API

### 5.1 教官列表查询
```http
GET /api/v1/instructors?page=1&size=20&keyword=李老师&speciality=Java&status=1
Authorization: Bearer {token}
```

### 5.2 创建教官
```http
POST /api/v1/instructors
Authorization: Bearer {token}
Content-Type: application/json

{
  "userId": 10,
  "instructorCode": "INST_001",
  "speciality": ["Java开发", "Spring框架", "微服务架构"],
  "qualification": [
    {
      "name": "高级工程师",
      "issuer": "工信部",
      "issueDate": "2020-06-01",
      "certificateNo": "GJ202006001"
    }
  ],
  "experienceYears": 5,
  "educationBackground": "计算机科学与技术本科",
  "professionalTitle": "高级工程师",
  "introduction": "资深Java开发工程师，具有丰富的项目经验",
  "teachingAreas": ["后端开发", "系统架构"],
  "contactInfo": {
    "phone": "13800138000",
    "email": "<EMAIL>",
    "wechat": "instructor_wx"
  },
  "hireDate": "2020-01-01"
}
```

### 5.3 教官详情
```http
GET /api/v1/instructors/{id}
Authorization: Bearer {token}
```

## 六、教案管理API

### 6.1 教案列表查询
```http
GET /api/v1/materials?page=1&size=20&courseId=1&instructorId=1&contentType=video&status=1
Authorization: Bearer {token}
```

### 6.2 上传教案
```http
POST /api/v1/materials
Authorization: Bearer {token}
Content-Type: application/json

{
  "materialName": "Java基础-第一章",
  "materialCode": "JAVA_BASIC_CH01",
  "courseId": 1,
  "instructorId": 1,
  "chapterNo": 1,
  "fileInfo": {
    "fileName": "java_basic_chapter1.pdf",
    "originalName": "Java基础-第一章.pdf",
    "fileSize": 2048576,
    "fileType": "pdf",
    "mimeType": "application/pdf",
    "fileUrl": "http://minio:9000/training-materials/java_basic_chapter1.pdf"
  },
  "contentType": "document",
  "duration": 120,
  "description": "Java基础知识第一章内容",
  "learningObjectives": "了解Java语言特点和开发环境",
  "keyPoints": "Java语言特性、JDK安装、Hello World程序",
  "difficultyLevel": "easy"
}
```

### 6.3 文件上传接口
```http
POST /api/v1/files/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
  "file": "binary_file_data",
  "businessType": "material",
  "businessId": 1
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "fileId": 123,
    "fileName": "java_basic_chapter1.pdf",
    "originalName": "Java基础-第一章.pdf",
    "fileSize": 2048576,
    "fileType": "pdf",
    "fileUrl": "http://minio:9000/training-materials/java_basic_chapter1.pdf",
    "thumbnailUrl": "http://minio:9000/training-materials/thumbnails/java_basic_chapter1.jpg"
  }
}
```

## 七、培训计划管理API

### 7.1 培训计划列表查询
```http
GET /api/v1/plans?page=1&size=20&keyword=Java培训&status=PUBLISHED&organizerDeptId=1
Authorization: Bearer {token}
```

### 7.2 创建培训计划
```http
POST /api/v1/plans
Authorization: Bearer {token}
Content-Type: application/json

{
  "planName": "2024年Java开发培训计划",
  "planCode": "PLAN_2024_JAVA_001",
  "planType": "regular",
  "trainingObjective": "提升团队Java开发技能",
  "targetAudience": "初级开发人员",
  "startDate": "2024-03-01",
  "endDate": "2024-03-31",
  "registrationStartDate": "2024-02-01",
  "registrationEndDate": "2024-02-28",
  "totalHours": 80,
  "maxParticipants": 30,
  "minParticipants": 10,
  "organizerDeptId": 1,
  "contactPersonId": 5,
  "location": "培训中心A座",
  "trainingMode": "hybrid",
  "requirements": "具备基本的编程基础",
  "scheduleInfo": {
    "sessions": [
      {
        "date": "2024-03-01",
        "startTime": "09:00",
        "endTime": "17:00",
        "courseId": 1,
        "instructorId": 1,
        "location": "教室A101"
      }
    ]
  },
  "budget": 50000.00
}
```

### 7.3 培训计划详情
```http
GET /api/v1/plans/{id}
Authorization: Bearer {token}
```

### 7.4 提交审核
```http
POST /api/v1/plans/{id}/submit
Authorization: Bearer {token}
```

### 7.5 审核培训计划
```http
POST /api/v1/plans/{id}/approve
Authorization: Bearer {token}
Content-Type: application/json

{
  "approvalStatus": "APPROVED",
  "approvalComment": "计划安排合理，同意执行"
}
```

### 7.6 发布培训计划
```http
POST /api/v1/plans/{id}/publish
Authorization: Bearer {token}
```

### 7.7 添加参训人员
```http
POST /api/v1/plans/{id}/trainees
Authorization: Bearer {token}
Content-Type: application/json

{
  "userIds": [10, 11, 12, 13],
  "enrollmentType": "manual"
}
```

### 7.8 获取参训人员列表
```http
GET /api/v1/plans/{id}/trainees?page=1&size=20&status=ENROLLED
Authorization: Bearer {token}
```

## 八、学习管理API

### 8.1 学习任务列表
```http
GET /api/v1/learning/tasks?page=1&size=20&planId=1&status=IN_PROGRESS&userId=10
Authorization: Bearer {token}
```

### 8.2 开始学习
```http
POST /api/v1/learning/tasks/{id}/start
Authorization: Bearer {token}
```

### 8.3 更新学习进度
```http
POST /api/v1/learning/progress
Authorization: Bearer {token}
Content-Type: application/json

{
  "taskId": 123,
  "sessionId": "session_uuid_123",
  "currentPosition": 1800,
  "duration": 30,
  "actionType": "progress",
  "deviceInfo": {
    "userAgent": "Mozilla/5.0...",
    "screenResolution": "1920x1080"
  }
}
```

### 8.4 完成学习任务
```http
POST /api/v1/learning/tasks/{id}/complete
Authorization: Bearer {token}
Content-Type: application/json

{
  "actualDuration": 7200,
  "learningNotes": "学习笔记内容"
}
```

### 8.5 学习统计
```http
GET /api/v1/learning/statistics?userId=10&planId=1&startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {token}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalTasks": 10,
    "completedTasks": 7,
    "inProgressTasks": 2,
    "notStartedTasks": 1,
    "totalStudyTime": 14400,
    "averageProgress": 75.5,
    "completionRate": 70.0,
    "dailyProgress": [
      {
        "date": "2024-01-01",
        "studyTime": 3600,
        "completedTasks": 1
      }
    ]
  }
}
```

## 九、考试管理API

### 9.1 考试列表查询
```http
GET /api/v1/exams?page=1&size=20&planId=1&courseId=1&status=PUBLISHED
Authorization: Bearer {token}
```

### 9.2 创建考试
```http
POST /api/v1/exams
Authorization: Bearer {token}
Content-Type: application/json

{
  "examName": "Java基础知识测试",
  "examCode": "EXAM_JAVA_BASIC_001",
  "planId": 1,
  "courseId": 1,
  "examType": "final",
  "description": "Java基础知识综合测试",
  "instructions": "请仔细阅读题目，独立完成答题",
  "totalScore": 100.00,
  "passScore": 60.00,
  "duration": 120,
  "startTime": "2024-03-15T09:00:00Z",
  "endTime": "2024-03-15T11:00:00Z",
  "shuffleQuestions": true,
  "shuffleOptions": true,
  "showResultImmediately": false,
  "allowReview": true,
  "maxAttempts": 1,
  "questionRules": [
    {
      "questionType": "single_choice",
      "difficulty": "easy",
      "count": 10,
      "scorePerQuestion": 2.0
    },
    {
      "questionType": "multiple_choice",
      "difficulty": "medium",
      "count": 5,
      "scorePerQuestion": 4.0
    }
  ]
}
```

### 9.3 开始考试
```http
POST /api/v1/exams/{id}/start
Authorization: Bearer {token}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "考试开始成功",
  "data": {
    "paperId": 456,
    "paperCode": "PAPER_20240315_001",
    "examInfo": {
      "examName": "Java基础知识测试",
      "duration": 120,
      "totalScore": 100.00,
      "questionCount": 15,
      "instructions": "请仔细阅读题目，独立完成答题"
    },
    "questions": [
      {
        "id": 1,
        "questionText": "以下哪个是Java的基本数据类型？",
        "questionType": "single_choice",
        "score": 2.0,
        "options": [
          {"key": "A", "value": "String"},
          {"key": "B", "value": "int"},
          {"key": "C", "value": "List"},
          {"key": "D", "value": "Map"}
        ]
      }
    ],
    "startTime": "2024-03-15T09:00:00Z",
    "endTime": "2024-03-15T11:00:00Z"
  }
}
```

### 9.4 提交答案
```http
POST /api/v1/exams/papers/{paperId}/submit
Authorization: Bearer {token}
Content-Type: application/json

{
  "answers": [
    {
      "questionId": 1,
      "answer": "B"
    },
    {
      "questionId": 2,
      "answer": ["A", "C"]
    }
  ],
  "submitType": "manual"
}
```

### 9.5 考试结果查询
```http
GET /api/v1/exams/{id}/results?page=1&size=20&userId=10
Authorization: Bearer {token}
```

这个API接口详细设计文档涵盖了教育培训系统的主要功能模块，提供了完整的接口规范和示例，为前后端开发提供了详细的技术指导。
```
