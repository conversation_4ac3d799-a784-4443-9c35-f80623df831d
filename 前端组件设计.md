# 教育培训系统前端组件设计

## 一、技术栈和架构

### 1.1 技术选型
- **框架**: Vue.js 3.4+ (Composition API)
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **构建工具**: Vite
- **语言**: TypeScript 5.0+
- **样式**: SCSS

### 1.2 项目结构
```
src/
├── api/                    # API接口
├── assets/                 # 静态资源
├── components/             # 公共组件
│   ├── common/            # 通用组件
│   ├── form/              # 表单组件
│   ├── table/             # 表格组件
│   └── chart/             # 图表组件
├── composables/           # 组合式函数
├── layouts/               # 布局组件
├── pages/                 # 页面组件
├── router/                # 路由配置
├── stores/                # 状态管理
├── styles/                # 样式文件
├── types/                 # TypeScript类型定义
├── utils/                 # 工具函数
└── main.ts               # 入口文件
```

## 二、核心组件设计

### 2.1 布局组件

#### 2.1.1 主布局组件 (MainLayout.vue)
```vue
<template>
  <el-container class="main-layout">
    <!-- 顶部导航 -->
    <el-header class="main-header">
      <AppHeader />
    </el-header>
    
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="main-sidebar">
        <AppSidebar />
      </el-aside>
      
      <!-- 主内容区 -->
      <el-main class="main-content">
        <div class="content-wrapper">
          <!-- 面包屑导航 -->
          <AppBreadcrumb />
          
          <!-- 页面内容 -->
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'
import AppHeader from './components/AppHeader.vue'
import AppSidebar from './components/AppSidebar.vue'
import AppBreadcrumb from './components/AppBreadcrumb.vue'

const appStore = useAppStore()

const sidebarWidth = computed(() => 
  appStore.sidebarCollapsed ? '64px' : '240px'
)
</script>
```

#### 2.1.2 顶部导航组件 (AppHeader.vue)
```vue
<template>
  <div class="app-header">
    <div class="header-left">
      <!-- Logo和标题 -->
      <div class="logo-section">
        <img src="@/assets/logo.png" alt="Logo" class="logo" />
        <h1 class="title">教育培训系统</h1>
      </div>
      
      <!-- 侧边栏折叠按钮 -->
      <el-button 
        type="text" 
        @click="toggleSidebar"
        class="sidebar-toggle"
      >
        <el-icon><Fold v-if="!sidebarCollapsed" /><Expand v-else /></el-icon>
      </el-button>
    </div>
    
    <div class="header-right">
      <!-- 通知 -->
      <el-dropdown trigger="click" class="notification-dropdown">
        <el-badge :value="unreadCount" :hidden="unreadCount === 0">
          <el-button type="text" class="notification-btn">
            <el-icon><Bell /></el-icon>
          </el-button>
        </el-badge>
        <template #dropdown>
          <NotificationDropdown />
        </template>
      </el-dropdown>
      
      <!-- 用户菜单 -->
      <el-dropdown trigger="click" class="user-dropdown">
        <div class="user-info">
          <el-avatar :src="userInfo.avatar" :size="32">
            {{ userInfo.realName?.charAt(0) }}
          </el-avatar>
          <span class="username">{{ userInfo.realName }}</span>
          <el-icon><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <UserDropdown />
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import NotificationDropdown from './NotificationDropdown.vue'
import UserDropdown from './UserDropdown.vue'

const appStore = useAppStore()
const userStore = useUserStore()

const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)
const userInfo = computed(() => userStore.userInfo)
const unreadCount = computed(() => userStore.unreadNotificationCount)

const toggleSidebar = () => {
  appStore.toggleSidebar()
}
</script>
```

### 2.2 表格组件

#### 2.2.1 数据表格组件 (DataTable.vue)
```vue
<template>
  <div class="data-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left">
          <el-button 
            v-if="showAdd" 
            type="primary" 
            @click="handleAdd"
          >
            <el-icon><Plus /></el-icon>
            新增
          </el-button>
          <el-button 
            v-if="showBatchDelete && selectedRows.length > 0" 
            type="danger" 
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </slot>
      </div>
      
      <div class="toolbar-right">
        <slot name="toolbar-right">
          <!-- 搜索框 -->
          <el-input
            v-if="showSearch"
            v-model="searchKeyword"
            placeholder="请输入关键词搜索"
            style="width: 240px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button @click="handleSearch">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
          
          <!-- 刷新按钮 -->
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </slot>
      </div>
    </div>
    
    <!-- 表格 -->
    <el-table
      ref="tableRef"
      :data="tableData"
      :loading="loading"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      v-bind="$attrs"
    >
      <!-- 选择列 -->
      <el-table-column 
        v-if="showSelection" 
        type="selection" 
        width="55" 
        align="center"
      />
      
      <!-- 序号列 -->
      <el-table-column 
        v-if="showIndex" 
        type="index" 
        label="序号" 
        width="60" 
        align="center"
      />
      
      <!-- 动态列 -->
      <template v-for="column in columns" :key="column.prop">
        <el-table-column v-bind="column">
          <template #default="scope" v-if="column.slot">
            <slot :name="column.slot" :row="scope.row" :index="scope.$index" />
          </template>
        </el-table-column>
      </template>
      
      <!-- 操作列 -->
      <el-table-column 
        v-if="showActions" 
        label="操作" 
        :width="actionWidth"
        align="center"
        fixed="right"
      >
        <template #default="scope">
          <slot name="actions" :row="scope.row" :index="scope.$index">
            <el-button 
              type="primary" 
              size="small" 
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </slot>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="table-pagination" v-if="showPagination">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import type { TableColumn, Pagination } from '@/types/table'

interface Props {
  columns: TableColumn[]
  data?: any[]
  loading?: boolean
  showToolbar?: boolean
  showAdd?: boolean
  showBatchDelete?: boolean
  showSearch?: boolean
  showSelection?: boolean
  showIndex?: boolean
  showActions?: boolean
  showPagination?: boolean
  actionWidth?: number
  pagination?: Pagination
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  loading: false,
  showToolbar: true,
  showAdd: true,
  showBatchDelete: true,
  showSearch: true,
  showSelection: true,
  showIndex: true,
  showActions: true,
  showPagination: true,
  actionWidth: 160
})

const emit = defineEmits<{
  add: []
  edit: [row: any]
  delete: [row: any]
  batchDelete: [rows: any[]]
  search: [keyword: string]
  refresh: []
  sizeChange: [size: number]
  currentChange: [current: number]
  sortChange: [sort: any]
}>()

const tableRef = ref()
const searchKeyword = ref('')
const selectedRows = ref<any[]>([])

const tableData = computed(() => props.data)
const pagination = reactive(props.pagination || {
  current: 1,
  size: 20,
  total: 0
})

const handleAdd = () => emit('add')
const handleEdit = (row: any) => emit('edit', row)
const handleDelete = (row: any) => emit('delete', row)
const handleBatchDelete = () => emit('batchDelete', selectedRows.value)
const handleSearch = () => emit('search', searchKeyword.value)
const handleRefresh = () => emit('refresh')
const handleSizeChange = (size: number) => emit('sizeChange', size)
const handleCurrentChange = (current: number) => emit('currentChange', current)
const handleSortChange = (sort: any) => emit('sortChange', sort)

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}
</script>
```

### 2.3 表单组件

#### 2.3.1 动态表单组件 (DynamicForm.vue)
```vue
<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    :label-width="labelWidth"
    v-bind="$attrs"
  >
    <template v-for="field in fields" :key="field.prop">
      <el-form-item 
        :label="field.label" 
        :prop="field.prop"
        :required="field.required"
      >
        <!-- 输入框 -->
        <el-input
          v-if="field.type === 'input'"
          v-model="formData[field.prop]"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          v-bind="field.attrs"
        />
        
        <!-- 密码框 -->
        <el-input
          v-else-if="field.type === 'password'"
          v-model="formData[field.prop]"
          type="password"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          show-password
          v-bind="field.attrs"
        />
        
        <!-- 文本域 -->
        <el-input
          v-else-if="field.type === 'textarea'"
          v-model="formData[field.prop]"
          type="textarea"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          v-bind="field.attrs"
        />
        
        <!-- 选择器 -->
        <el-select
          v-else-if="field.type === 'select'"
          v-model="formData[field.prop]"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          v-bind="field.attrs"
        >
          <el-option
            v-for="option in field.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <!-- 日期选择器 -->
        <el-date-picker
          v-else-if="field.type === 'date'"
          v-model="formData[field.prop]"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          v-bind="field.attrs"
        />
        
        <!-- 数字输入框 -->
        <el-input-number
          v-else-if="field.type === 'number'"
          v-model="formData[field.prop]"
          :placeholder="field.placeholder"
          :disabled="field.disabled"
          v-bind="field.attrs"
        />
        
        <!-- 开关 -->
        <el-switch
          v-else-if="field.type === 'switch'"
          v-model="formData[field.prop]"
          :disabled="field.disabled"
          v-bind="field.attrs"
        />
        
        <!-- 单选框组 -->
        <el-radio-group
          v-else-if="field.type === 'radio'"
          v-model="formData[field.prop]"
          :disabled="field.disabled"
        >
          <el-radio
            v-for="option in field.options"
            :key="option.value"
            :label="option.value"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
        
        <!-- 复选框组 -->
        <el-checkbox-group
          v-else-if="field.type === 'checkbox'"
          v-model="formData[field.prop]"
          :disabled="field.disabled"
        >
          <el-checkbox
            v-for="option in field.options"
            :key="option.value"
            :label="option.value"
          >
            {{ option.label }}
          </el-checkbox>
        </el-checkbox-group>
        
        <!-- 文件上传 -->
        <FileUpload
          v-else-if="field.type === 'upload'"
          v-model="formData[field.prop]"
          :disabled="field.disabled"
          v-bind="field.attrs"
        />
        
        <!-- 自定义插槽 -->
        <slot 
          v-else-if="field.type === 'slot'" 
          :name="field.slot" 
          :field="field"
          :value="formData[field.prop]"
        />
      </el-form-item>
    </template>
    
    <!-- 表单按钮 -->
    <el-form-item v-if="showButtons">
      <slot name="buttons">
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </slot>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { FormField } from '@/types/form'
import FileUpload from './FileUpload.vue'

interface Props {
  fields: FormField[]
  modelValue?: Record<string, any>
  labelWidth?: string
  showButtons?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  labelWidth: '120px',
  showButtons: true
})

const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>]
  submit: [data: Record<string, any>]
  reset: []
}>()

const formRef = ref()
const formData = reactive({ ...props.modelValue })

// 生成表单验证规则
const formRules = computed(() => {
  const rules: Record<string, any> = {}
  props.fields.forEach(field => {
    if (field.rules) {
      rules[field.prop] = field.rules
    } else if (field.required) {
      rules[field.prop] = [
        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
      ]
    }
  })
  return rules
})

// 监听表单数据变化
watch(formData, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(formData, newValue)
}, { deep: true })

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', formData)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleReset = () => {
  formRef.value?.resetFields()
  emit('reset')
}

// 暴露表单方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: () => formRef.value?.clearValidate()
})
</script>
```

这个前端组件设计文档提供了教育培训系统前端开发的核心组件架构，包括布局组件、表格组件和表单组件的详细实现，为前端开发提供了完整的组件化解决方案。
