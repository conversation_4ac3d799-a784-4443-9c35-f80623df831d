version: '3.8'

services:
  # PostgreSQL数据库
  postgresql:
    image: postgres:15-alpine
    container_name: training-postgresql
    environment:
      POSTGRES_DB: training_system
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - ./docs/database/schema:/docker-entrypoint-initdb.d
    networks:
      - training-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: training-redis
    command: redis-server --requirepass redis123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - training-network
    restart: unless-stopped

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: training-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"  # API端口
      - "9001:9001"  # 控制台端口
    volumes:
      - minio_data:/data
    networks:
      - training-network
    restart: unless-stopped

  # Spring Boot后端应用
  training-backend:
    build: 
      context: ./training-backend
      dockerfile: Dockerfile
    container_name: training-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: *************************************************
      SPRING_DATASOURCE_USERNAME: postgres
      SPRING_DATASOURCE_PASSWORD: postgres123
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_PASSWORD: redis123
      MINIO_ENDPOINT: http://minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123
    ports:
      - "8080:8080"
    depends_on:
      - postgresql
      - redis
      - minio
    networks:
      - training-network
    restart: unless-stopped

  # Vue.js前端应用
  training-frontend:
    build:
      context: ./training-frontend
      dockerfile: Dockerfile
    container_name: training-frontend
    environment:
      VITE_API_BASE_URL: http://localhost:8080/api
    ports:
      - "3000:3000"
    depends_on:
      - training-backend
    networks:
      - training-network
    restart: unless-stopped

volumes:
  postgresql_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local

networks:
  training-network:
    driver: bridge

# 开发环境快速启动命令：
# docker-compose up -d postgresql redis minio
# 
# 完整启动命令：
# docker-compose up -d
#
# 查看日志：
# docker-compose logs -f training-backend
#
# 停止服务：
# docker-compose down
#
# 清理数据：
# docker-compose down -v
