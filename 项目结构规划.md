# 教育培训系统项目结构规划

## 一、整体项目结构

```
training-system/
├── training-backend/              # Spring Boot后端应用
├── training-frontend/             # Vue.js前端应用
├── docker-compose.yml             # Docker编排文件
├── README.md                      # 项目说明
└── docs/                          # 项目文档
    ├── api/                       # API文档
    ├── database/                  # 数据库设计文档
    └── deployment/                # 部署文档
```

## 二、后端应用结构 (training-backend)

```
training-backend/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/training/
│   │   │       ├── TrainingApplication.java          # 主启动类
│   │   │       ├── config/                           # 配置类
│   │   │       │   ├── SecurityConfig.java           # Spring Security配置
│   │   │       │   ├── JwtConfig.java                # JWT配置
│   │   │       │   ├── RedisConfig.java              # Redis配置
│   │   │       │   ├── MinioConfig.java              # MinIO配置
│   │   │       │   ├── WebConfig.java                # Web配置
│   │   │       │   └── SwaggerConfig.java            # API文档配置
│   │   │       ├── controller/                       # 控制器层
│   │   │       │   ├── auth/                         # 认证相关
│   │   │       │   │   ├── AuthController.java
│   │   │       │   │   └── UserController.java
│   │   │       │   ├── resource/                     # 资源管理
│   │   │       │   │   ├── CourseController.java
│   │   │       │   │   ├── InstructorController.java
│   │   │       │   │   ├── MaterialController.java
│   │   │       │   │   └── QuestionController.java
│   │   │       │   ├── plan/                         # 计划管理
│   │   │       │   │   └── TrainingPlanController.java
│   │   │       │   ├── learning/                     # 学习管理
│   │   │       │   │   └── LearningController.java
│   │   │       │   ├── exam/                         # 考试管理
│   │   │       │   │   └── ExamController.java
│   │   │       │   ├── file/                         # 文件管理
│   │   │       │   │   └── FileController.java
│   │   │       │   └── system/                       # 系统管理
│   │   │       │       └── SystemController.java
│   │   │       ├── service/                          # 服务层
│   │   │       │   ├── auth/
│   │   │       │   │   ├── AuthService.java
│   │   │       │   │   ├── UserService.java
│   │   │       │   │   └── RoleService.java
│   │   │       │   ├── resource/
│   │   │       │   │   ├── CourseService.java
│   │   │       │   │   ├── InstructorService.java
│   │   │       │   │   ├── MaterialService.java
│   │   │       │   │   └── QuestionService.java
│   │   │       │   ├── plan/
│   │   │       │   │   └── TrainingPlanService.java
│   │   │       │   ├── learning/
│   │   │       │   │   └── LearningService.java
│   │   │       │   ├── exam/
│   │   │       │   │   └── ExamService.java
│   │   │       │   └── file/
│   │   │       │       └── FileService.java
│   │   │       ├── repository/                       # 数据访问层
│   │   │       │   ├── auth/
│   │   │       │   │   ├── UserRepository.java
│   │   │       │   │   ├── RoleRepository.java
│   │   │       │   │   └── UserRoleRepository.java
│   │   │       │   ├── resource/
│   │   │       │   │   ├── CourseRepository.java
│   │   │       │   │   ├── InstructorRepository.java
│   │   │       │   │   ├── MaterialRepository.java
│   │   │       │   │   └── QuestionRepository.java
│   │   │       │   ├── plan/
│   │   │       │   │   ├── TrainingPlanRepository.java
│   │   │       │   │   ├── PlanCourseRepository.java
│   │   │       │   │   └── PlanTraineeRepository.java
│   │   │       │   ├── learning/
│   │   │       │   │   ├── LearningTaskRepository.java
│   │   │       │   │   └── LearningProgressRepository.java
│   │   │       │   └── exam/
│   │   │       │       ├── ExamRepository.java
│   │   │       │       ├── ExamPaperRepository.java
│   │   │       │       └── ExamResultRepository.java
│   │   │       ├── entity/                           # 实体类
│   │   │       │   ├── auth/
│   │   │       │   │   ├── User.java
│   │   │       │   │   ├── Role.java
│   │   │       │   │   └── UserRole.java
│   │   │       │   ├── resource/
│   │   │       │   │   ├── Course.java
│   │   │       │   │   ├── Instructor.java
│   │   │       │   │   ├── Material.java
│   │   │       │   │   └── Question.java
│   │   │       │   ├── plan/
│   │   │       │   │   ├── TrainingPlan.java
│   │   │       │   │   ├── PlanCourse.java
│   │   │       │   │   └── PlanTrainee.java
│   │   │       │   ├── learning/
│   │   │       │   │   ├── LearningTask.java
│   │   │       │   │   └── LearningProgress.java
│   │   │       │   └── exam/
│   │   │       │       ├── Exam.java
│   │   │       │       ├── ExamPaper.java
│   │   │       │       └── ExamResult.java
│   │   │       ├── dto/                              # 数据传输对象
│   │   │       │   ├── auth/
│   │   │       │   │   ├── LoginRequest.java
│   │   │       │   │   ├── LoginResponse.java
│   │   │       │   │   └── UserDTO.java
│   │   │       │   ├── resource/
│   │   │       │   │   ├── CourseDTO.java
│   │   │       │   │   ├── InstructorDTO.java
│   │   │       │   │   ├── MaterialDTO.java
│   │   │       │   │   └── QuestionDTO.java
│   │   │       │   └── common/
│   │   │       │       ├── PageRequest.java
│   │   │       │       └── PageResponse.java
│   │   │       ├── common/                           # 公共组件
│   │   │       │   ├── annotation/
│   │   │       │   │   ├── Log.java
│   │   │       │   │   └── RequirePermission.java
│   │   │       │   ├── aspect/
│   │   │       │   │   ├── LogAspect.java
│   │   │       │   │   └── PermissionAspect.java
│   │   │       │   ├── constant/
│   │   │       │   │   ├── CommonConstants.java
│   │   │       │   │   └── RedisConstants.java
│   │   │       │   ├── exception/
│   │   │       │   │   ├── BusinessException.java
│   │   │       │   │   ├── SystemException.java
│   │   │       │   │   └── GlobalExceptionHandler.java
│   │   │       │   ├── result/
│   │   │       │   │   ├── Result.java
│   │   │       │   │   └── ResultCode.java
│   │   │       │   ├── util/
│   │   │       │   │   ├── DateUtils.java
│   │   │       │   │   ├── StringUtils.java
│   │   │       │   │   ├── JsonUtils.java
│   │   │       │   │   ├── EncryptUtils.java
│   │   │       │   │   └── FileUtils.java
│   │   │       │   └── security/
│   │   │       │       ├── JwtTokenProvider.java
│   │   │       │       ├── UserDetailsServiceImpl.java
│   │   │       │       └── SecurityUtils.java
│   │   │       └── base/                             # 基础类
│   │   │           ├── BaseEntity.java
│   │   │           ├── BaseController.java
│   │   │           └── BaseService.java
│   │   └── resources/
│   │       ├── application.yml                       # 主配置文件
│   │       ├── application-dev.yml                   # 开发环境配置
│   │       ├── application-prod.yml                  # 生产环境配置
│   │       ├── db/
│   │       │   └── migration/                        # 数据库迁移脚本
│   │       │       ├── V1__Create_user_tables.sql
│   │       │       ├── V2__Create_resource_tables.sql
│   │       │       ├── V3__Create_plan_tables.sql
│   │       │       ├── V4__Create_learning_tables.sql
│   │       │       ├── V5__Create_exam_tables.sql
│   │       │       └── V6__Insert_initial_data.sql
│   │       ├── static/                               # 静态资源
│   │       └── templates/                            # 模板文件
│   └── test/
│       └── java/
│           └── com/training/
│               ├── controller/                       # 控制器测试
│               ├── service/                          # 服务测试
│               └── repository/                       # 数据访问测试
├── pom.xml                                           # Maven配置
├── Dockerfile                                        # Docker构建文件
└── README.md                                         # 项目说明
```

## 三、前端项目结构 (training-frontend)
```
training-frontend/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── main.ts
│   ├── App.vue
│   ├── api/
│   │   ├── auth.ts
│   │   ├── course.ts
│   │   ├── instructor.ts
│   │   ├── material.ts
│   │   ├── question.ts
│   │   ├── plan.ts
│   │   └── exam.ts
│   ├── components/
│   │   ├── common/
│   │   │   ├── Header.vue
│   │   │   ├── Sidebar.vue
│   │   │   ├── Breadcrumb.vue
│   │   │   └── Pagination.vue
│   │   ├── form/
│   │   │   ├── CourseForm.vue
│   │   │   ├── InstructorForm.vue
│   │   │   └── QuestionForm.vue
│   │   └── table/
│   │       ├── CourseTable.vue
│   │       ├── InstructorTable.vue
│   │       └── QuestionTable.vue
│   ├── views/
│   │   ├── login/
│   │   │   └── Login.vue
│   │   ├── dashboard/
│   │   │   └── Dashboard.vue
│   │   ├── resource/
│   │   │   ├── Course.vue
│   │   │   ├── Instructor.vue
│   │   │   ├── Material.vue
│   │   │   └── Question.vue
│   │   ├── plan/
│   │   │   ├── PlanList.vue
│   │   │   ├── PlanForm.vue
│   │   │   └── PlanDetail.vue
│   │   └── exam/
│   │       ├── ExamList.vue
│   │       ├── ExamForm.vue
│   │       └── ExamResult.vue
│   ├── router/
│   │   └── index.ts
│   ├── store/
│   │   ├── index.ts
│   │   ├── modules/
│   │   │   ├── auth.ts
│   │   │   ├── user.ts
│   │   │   └── app.ts
│   │   └── types.ts
│   ├── utils/
│   │   ├── request.ts
│   │   ├── auth.ts
│   │   ├── storage.ts
│   │   └── validate.ts
│   ├── styles/
│   │   ├── index.scss
│   │   ├── variables.scss
│   │   └── mixins.scss
│   └── types/
│       ├── api.ts
│       ├── user.ts
│       ├── course.ts
│       └── common.ts
├── package.json
├── vite.config.ts
├── tsconfig.json
├── .env.development
├── .env.production
└── Dockerfile
```

## 四、数据库设计文件结构

### 4.1 数据库脚本组织
```
docs/database/
├── schema/
│   ├── 01_user_management.sql      # 用户管理相关表
│   ├── 02_resource_management.sql  # 资源管理相关表
│   ├── 03_plan_management.sql      # 计划管理相关表
│   ├── 04_learning_management.sql  # 学习管理相关表
│   ├── 05_exam_management.sql      # 考试管理相关表
│   └── 06_system_management.sql    # 系统管理相关表
├── data/
│   ├── init_roles.sql              # 初始角色数据
│   ├── init_permissions.sql        # 初始权限数据
│   ├── init_admin_user.sql         # 初始管理员用户
│   └── demo_data.sql               # 演示数据
├── indexes/
│   └── performance_indexes.sql     # 性能优化索引
└── procedures/
    ├── user_procedures.sql         # 用户相关存储过程
    └── report_procedures.sql       # 报表相关存储过程
```

## 五、部署配置文件

### 5.1 Docker Compose 配置
```yaml
# docker-compose.yml 主要服务配置
version: '3.8'
services:
  # 基础设施服务
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: training_system
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docs/database/schema:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"

  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  nacos:
    image: nacos/nacos-server:v2.2.3
    environment:
      MODE: standalone
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_DB_NAME: nacos
    ports:
      - "8848:8848"
    depends_on:
      - mysql

  # 微服务
  training-gateway:
    build: ./training-gateway
    ports:
      - "8080:8080"
    depends_on:
      - nacos
      - redis

  training-auth:
    build: ./training-auth
    depends_on:
      - mysql
      - redis
      - nacos

  training-resource:
    build: ./training-resource
    depends_on:
      - mysql
      - redis
      - nacos

volumes:
  mysql_data:
  redis_data:
```

这个项目结构规划为您的教育培训系统提供了清晰的代码组织方式。每个微服务都有独立的结构，便于团队协作开发和后期维护。您希望我开始实现哪个具体的模块呢？
