# 教育培训系统开发实施指南

## 一、开发环境准备

### 1.1 必需软件安装
```bash
# Java开发环境
- JDK 17+
- Maven 3.8+
- IntelliJ IDEA 2023+

# 前端开发环境
- Node.js 18+
- npm 9+ 或 yarn 1.22+
- VS Code

# 数据库和中间件
- PostgreSQL 15+
- Redis 7.0+
- Min<PERSON> (对象存储)
- Docker & Docker Compose

# 开发工具
- <PERSON>it
- Postman (API测试)
- pgAdmin (PostgreSQL管理工具)
```

### 1.2 开发环境配置
```bash
# 1. 克隆项目
git clone <repository-url>
cd training-system

# 2. 启动基础设施
docker-compose up -d postgresql redis minio

# 3. 创建数据库
psql -h localhost -U postgres
CREATE DATABASE training_system;

# 4. 执行数据库脚本
psql -h localhost -U postgres -d training_system -f docs/database/schema/01_user_management.sql
```

## 二、第一阶段：基础框架搭建（3-4周）

### 2.1 Week 1: 项目初始化和基础配置

#### 2.1.1 创建Spring Boot项目
```bash
# 使用Spring Initializr创建项目
mvn archetype:generate \
  -DgroupId=com.training \
  -DartifactId=training-backend \
  -DarchetypeArtifactId=maven-archetype-quickstart \
  -DinteractiveMode=false

cd training-backend
```

#### 2.1.2 配置POM依赖
```xml
<!-- training-backend/pom.xml -->
<properties>
    <java.version>17</java.version>
    <spring-boot.version>3.2.0</spring-boot.version>
    <postgresql.version>42.7.1</postgresql.version>
    <redis.version>3.2.0</redis.version>
    <minio.version>8.5.7</minio.version>
    <jwt.version>0.12.3</jwt.version>
</properties>

<dependencies>
    <!-- Spring Boot Starters -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-security</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <!-- Database -->
    <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>${postgresql.version}</version>
    </dependency>

    <!-- MinIO -->
    <dependency>
        <groupId>io.minio</groupId>
        <artifactId>minio</artifactId>
        <version>${minio.version}</version>
    </dependency>

    <!-- JWT -->
    <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
        <version>${jwt.version}</version>
    </dependency>
    <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-impl</artifactId>
        <version>${jwt.version}</version>
    </dependency>
    <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-jackson</artifactId>
        <version>${jwt.version}</version>
    </dependency>

    <!-- API Documentation -->
    <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        <version>4.4.0</version>
    </dependency>
</dependencies>
```

#### 2.1.3 开发公共模块核心类
**任务清单：**
- [ ] 创建统一响应结果类 (Result.java)
- [ ] 创建全局异常处理器 (GlobalExceptionHandler.java)
- [ ] 创建基础实体类 (BaseEntity.java)
- [ ] 创建常用工具类 (DateUtils, StringUtils, JsonUtils)
- [ ] 创建分页请求和响应类
- [ ] 配置MyBatis-Plus和Redis

### 2.2 Week 3-4: 认证授权服务

#### 2.2.1 创建认证服务模块
```bash
mvn archetype:generate -DgroupId=com.training -DartifactId=training-auth -DarchetypeArtifactId=maven-archetype-quickstart
```

#### 2.2.2 核心功能开发
**任务清单：**
- [ ] 用户实体类和数据访问层
- [ ] JWT Token生成和验证
- [ ] 用户登录/登出接口
- [ ] 用户信息管理接口
- [ ] 角色权限管理
- [ ] Spring Security配置
- [ ] Redis会话管理

#### 2.2.3 关键代码示例
```java
// JWT配置类
@Configuration
public class JwtConfig {
    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration}")
    private Long expiration;

    @Bean
    public JwtTokenProvider jwtTokenProvider() {
        return new JwtTokenProvider(secret, expiration);
    }
}

// 登录控制器
@RestController
@RequestMapping("/api/v1/auth")
public class AuthController {

    @Autowired
    private AuthService authService;

    @PostMapping("/login")
    public Result<LoginResponse> login(@RequestBody LoginRequest request) {
        return Result.success(authService.login(request));
    }
}
```

### 2.3 Week 5-6: API网关和服务注册

#### 2.3.1 创建网关服务
```bash
mvn archetype:generate -DgroupId=com.training -DartifactId=training-gateway -DarchetypeArtifactId=maven-archetype-quickstart
```

#### 2.3.2 网关配置开发
**任务清单：**
- [ ] Spring Cloud Gateway配置
- [ ] 路由规则配置
- [ ] 认证过滤器
- [ ] 跨域配置
- [ ] 限流配置
- [ ] 日志记录

## 三、第二阶段：核心业务开发（8-10周）

### 3.1 Week 7-10: 资源管理服务

#### 3.1.1 数据库表创建
```sql
-- 执行资源管理相关表脚本
mysql -h localhost -u root -p training_system < docs/database/schema/02_resource_management.sql
```

#### 3.1.2 开发任务分解
**Week 7: 课程管理模块**
- [ ] Course实体类和Repository
- [ ] CourseService业务逻辑
- [ ] CourseController接口
- [ ] 课程分类管理
- [ ] 课程搜索功能

**Week 8: 教官管理模块**
- [ ] Instructor实体类和Repository
- [ ] InstructorService业务逻辑
- [ ] InstructorController接口
- [ ] 教官资质管理
- [ ] 教官课程关联

**Week 9: 教案管理模块**
- [ ] Material实体类和Repository
- [ ] MaterialService业务逻辑
- [ ] MaterialController接口
- [ ] 文件上传功能
- [ ] 教案版本管理

**Week 10: 考题管理模块**
- [ ] Question实体类和Repository
- [ ] QuestionService业务逻辑
- [ ] QuestionController接口
- [ ] 题目分类和难度管理
- [ ] 题库导入导出

### 3.2 Week 11-14: 计划管理服务

#### 3.2.1 开发任务分解
**Week 11-12: 培训计划基础功能**
- [ ] TrainingPlan实体类和Repository
- [ ] 计划创建和编辑
- [ ] 课程安排功能
- [ ] 参训人员管理

**Week 13-14: 审核流程和发布**
- [ ] 审核流程设计
- [ ] 审核状态管理
- [ ] 计划发布功能
- [ ] 通知集成

### 3.3 Week 15-16: 前端基础框架

#### 3.3.1 管理端前端初始化
```bash
# 创建Vue3项目
npm create vue@latest training-admin-ui
cd training-admin-ui
npm install

# 安装依赖
npm install element-plus @element-plus/icons-vue
npm install axios pinia vue-router@4
npm install @types/node typescript
```

#### 3.3.2 前端开发任务
- [ ] 项目结构搭建
- [ ] 路由配置
- [ ] 状态管理配置
- [ ] HTTP请求封装
- [ ] 登录页面
- [ ] 主框架布局
- [ ] 资源管理页面

## 四、第三阶段：学习考试模块（6-8周）

### 4.1 Week 17-20: 学习管理服务

#### 4.1.1 数据库设计
```sql
-- 学习相关表
CREATE TABLE learning_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    plan_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    material_id BIGINT NOT NULL,
    status VARCHAR(20) DEFAULT 'NOT_STARTED',
    progress INT DEFAULT 0,
    start_time DATETIME,
    complete_time DATETIME,
    study_duration INT DEFAULT 0,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE learning_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    progress_time DATETIME,
    current_position INT,
    duration INT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 4.1.2 开发任务
- [ ] 学习任务生成逻辑
- [ ] 学习进度跟踪
- [ ] 学习时长统计
- [ ] 学习报告生成

### 4.2 Week 21-24: 考试管理服务

#### 4.2.1 考试功能开发
- [ ] 试卷生成算法
- [ ] 在线答题功能
- [ ] 自动评分系统
- [ ] 成绩统计分析
- [ ] 防作弊机制

## 五、第四阶段：优化完善（4-6周）

### 5.1 Week 25-26: 性能优化
- [ ] 数据库查询优化
- [ ] Redis缓存策略
- [ ] 接口性能测试
- [ ] 前端性能优化

### 5.2 Week 27-28: 安全加固
- [ ] 接口权限控制
- [ ] 数据加密
- [ ] 安全漏洞扫描
- [ ] 日志审计

### 5.3 Week 29-30: 测试和部署
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 用户验收测试
- [ ] 生产环境部署

## 六、开发规范和最佳实践

### 6.1 代码规范
- 使用阿里巴巴Java开发手册
- 统一的命名规范
- 完善的注释文档
- 代码审查机制

### 6.2 Git工作流
```bash
# 功能分支开发流程
git checkout -b feature/user-management
# 开发完成后
git add .
git commit -m "feat: 添加用户管理功能"
git push origin feature/user-management
# 创建Pull Request进行代码审查
```

### 6.3 测试策略
- 单元测试覆盖率 > 80%
- 集成测试覆盖核心业务流程
- 性能测试验证系统负载能力
- 安全测试确保系统安全性

这个开发实施指南为您的团队提供了详细的开发路线图和具体的实施步骤。建议按照这个计划逐步推进，确保每个阶段的质量和进度。您希望我开始实现哪个具体的模块呢？
